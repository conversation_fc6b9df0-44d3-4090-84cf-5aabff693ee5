/* تحسينات احترافية للتصميم */

/* تحسين الخطوط والنصوص */
body {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسين التدرجات اللونية */
.gradient-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 50%, var(--primary-700) 100%);
}

.gradient-secondary {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسين الظلال */
.shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-strong {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 10px -2px rgba(0, 0, 0, 0.05);
}

.dark-mode .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
}

.dark-mode .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

.dark-mode .shadow-strong {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.5), 0 2px 10px -2px rgba(0, 0, 0, 0.3);
}

/* تحسين الحدود */
.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(var(--bg-primary), var(--bg-primary)) padding-box,
                linear-gradient(135deg, var(--primary-500), var(--primary-700)) border-box;
}

.border-soft {
    border: 1px solid var(--border-color-light);
}

.border-medium {
    border: 1px solid var(--border-color);
}

/* تحسين الأزرار */
.btn-gradient {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-gradient:hover::before {
    opacity: 1;
}

.btn-gradient span {
    position: relative;
    z-index: 1;
}

/* تحسين البطاقات */
.card-premium {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.card-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600), var(--primary-700));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.card-premium:hover::before {
    transform: scaleX(1);
}

.card-premium:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
    border-color: var(--primary-200);
}

/* تحسين الأيقونات */
.icon-container {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-100);
    color: var(--primary-600);
    font-size: 1.5rem;
    transition: var(--transition);
}

.dark-mode .icon-container {
    background: var(--primary-900);
    color: var(--primary-300);
}

.icon-container:hover {
    transform: scale(1.1) rotate(5deg);
    background: var(--primary-200);
}

.dark-mode .icon-container:hover {
    background: var(--primary-800);
}

/* تحسين النماذج */
.form-premium .form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-premium .form-label {
    position: absolute;
    top: 0.75rem;
    left: 1rem;
    color: var(--text-tertiary);
    transition: var(--transition);
    pointer-events: none;
    background: var(--bg-primary);
    padding: 0 0.5rem;
}

[dir="rtl"] .form-premium .form-label {
    left: auto;
    right: 1rem;
}

.form-premium .form-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
}

.form-premium .form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-premium .form-input:focus + .form-label,
.form-premium .form-input:not(:placeholder-shown) + .form-label {
    top: -0.5rem;
    font-size: 0.8rem;
    color: var(--primary-600);
}

/* تحسين الجداول */
.table-premium {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

.table-premium th,
.table-premium td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color-light);
}

[dir="ltr"] .table-premium th,
[dir="ltr"] .table-premium td {
    text-align: left;
}

.table-premium th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
}

.table-premium tr:hover {
    background: var(--bg-secondary);
}

/* تحسين القوائم */
.list-premium {
    list-style: none;
    padding: 0;
}

.list-premium li {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color-light);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.list-premium li:hover {
    background: var(--bg-secondary);
    padding-right: 1.5rem;
}

[dir="ltr"] .list-premium li:hover {
    padding-right: 1rem;
    padding-left: 1.5rem;
}

.list-premium li::before {
    content: '✓';
    width: 20px;
    height: 20px;
    background: var(--primary-500);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
    flex-shrink: 0;
}

/* تحسين الشارات */
.badge-premium {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
    background: var(--primary-100);
    color: var(--primary-800);
    border: 1px solid var(--primary-200);
}

.dark-mode .badge-premium {
    background: var(--primary-900);
    color: var(--primary-200);
    border-color: var(--primary-800);
}

.badge-success {
    background: var(--success-50);
    color: var(--success-600);
    border-color: var(--success-200);
}

.badge-warning {
    background: var(--warning-50);
    color: var(--warning-600);
    border-color: var(--warning-200);
}

.badge-error {
    background: var(--error-50);
    color: var(--error-600);
    border-color: var(--error-200);
}

/* تحسين التنبيهات */
.alert-premium {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-500);
    background: var(--primary-50);
    color: var(--primary-800);
    margin-bottom: 1rem;
}

[dir="ltr"] .alert-premium {
    border-left: none;
    border-right: 4px solid var(--primary-500);
}

.dark-mode .alert-premium {
    background: var(--primary-900);
    color: var(--primary-200);
}

.alert-success {
    border-color: var(--success-500);
    background: var(--success-50);
    color: var(--success-800);
}

.alert-warning {
    border-color: var(--warning-500);
    background: var(--warning-50);
    color: var(--warning-800);
}

.alert-error {
    border-color: var(--error-500);
    background: var(--error-50);
    color: var(--error-800);
}

/* تحسين التقدم */
.progress-premium {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* تحسين التبويبات */
.tabs-premium {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.tabs-list {
    display: flex;
    gap: 0;
    list-style: none;
    padding: 0;
    margin: 0;
}

.tab-item {
    padding: 1rem 2rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 2px solid transparent;
    font-weight: 500;
}

.tab-item:hover,
.tab-item.active {
    color: var(--primary-600);
    border-bottom-color: var(--primary-600);
}

.tab-content {
    padding: 2rem 0;
}

/* تحسين الأكورديون */
.accordion-premium {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.accordion-item {
    border-bottom: 1px solid var(--border-color);
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    padding: 1.5rem;
    background: var(--bg-secondary);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.accordion-header:hover {
    background: var(--bg-tertiary);
}

.accordion-content {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.accordion-item.active .accordion-content {
    padding: 1.5rem;
    max-height: 500px;
}

.accordion-icon {
    transition: transform 0.3s ease;
}

.accordion-item.active .accordion-icon {
    transform: rotate(180deg);
}

/* تحسين التحميل */
.loading-premium {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.spinner-premium {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-500);
    border-radius: 50%;
    animation: spin-premium 1s linear infinite;
}

@keyframes spin-premium {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الشبكات */
.grid-premium {
    display: grid;
    gap: 2rem;
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
}

/* تحسين المسافات */
.spacing-xs { margin: 0.5rem; }
.spacing-sm { margin: 1rem; }
.spacing-md { margin: 1.5rem; }
.spacing-lg { margin: 2rem; }
.spacing-xl { margin: 3rem; }

.padding-xs { padding: 0.5rem; }
.padding-sm { padding: 1rem; }
.padding-md { padding: 1.5rem; }
.padding-lg { padding: 2rem; }
.padding-xl { padding: 3rem; }
