/* لوحة التحكم - تصميم احترافي */

/* Dashboard Page Layout */
.dashboard-page {
    background: var(--bg-secondary);
    min-height: 100vh;
}

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.dashboard-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 100%;
    padding: 0 2rem;
    gap: 2rem;
}

.dashboard-logo {
    flex-shrink: 0;
}

.header-title {
    flex: 1;
    min-width: 0;
}

.header-title h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.breadcrumb {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

/* Notification Dropdown */
.notification-dropdown {
    position: relative;
}

.notification-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    position: relative;
}

.notification-btn:hover {
    background: var(--bg-secondary);
    color: var(--primary-600);
}

.notification-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--error-500);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.notification-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 350px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
}

.notification-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notification-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.notification-count {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color-light);
    display: flex;
    gap: 0.75rem;
    transition: var(--transition-fast);
}

.notification-item:hover {
    background: var(--bg-secondary);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item .notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-item .notification-icon.success {
    background: var(--success-100);
    color: var(--success-600);
}

.notification-item .notification-icon.info {
    background: var(--primary-100);
    color: var(--primary-600);
}

.notification-item .notification-icon.warning {
    background: var(--warning-100);
    color: var(--warning-600);
}

.notification-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.notification-content p {
    margin: 0 0 0.25rem 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.notification-time {
    font-size: 0.7rem;
    color: var(--text-tertiary);
}

.notification-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.notification-footer a {
    color: var(--primary-600);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

/* User Dropdown */
.user-dropdown {
    position: relative;
}

.user-btn {
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-btn:hover {
    background: var(--bg-secondary);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-size: 0.9rem;
    font-weight: 500;
}

.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
}

.user-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-info {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    align-items: center;
}

.user-avatar-large {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.user-details h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.user-details span {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.user-menu-items {
    padding: 0.5rem 0;
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
    background: none;
    border: none;
    width: 100%;
    text-align: right;
    cursor: pointer;
    font-size: 0.9rem;
}

[dir="ltr"] .user-menu-item {
    text-align: left;
}

.user-menu-item:hover {
    background: var(--bg-secondary);
}

.user-menu-item i {
    width: 16px;
    text-align: center;
}

.user-menu-divider {
    height: 1px;
    background: var(--border-color);
    margin: 0.5rem 0;
}

.logout-btn {
    color: var(--error-600) !important;
}

.logout-btn:hover {
    background: var(--error-50) !important;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
}

/* Dashboard Layout */
.dashboard-layout {
    display: flex;
    padding-top: 80px;
    min-height: calc(100vh - 80px);
}

/* Sidebar */
.dashboard-sidebar {
    width: 280px;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-right: 1px solid var(--border-color);
    padding: 2rem 0;
    position: fixed;
    top: 80px;
    left: 0;
    height: calc(100vh - 80px);
    overflow-y: auto;
    z-index: 100;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

[dir="rtl"] .dashboard-sidebar {
    left: auto;
    right: 0;
    border-right: none;
    border-left: 1px solid var(--border-color);
}

.sidebar-nav {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    flex: 1;
}

.sidebar-item {
    margin-bottom: 0.5rem;
}

.sidebar-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
    font-weight: 500;
    border-right: 3px solid transparent;
}

[dir="rtl"] .sidebar-link {
    border-right: none;
    border-left: 3px solid transparent;
}

.sidebar-link:hover,
.sidebar-item.active .sidebar-link {
    background: var(--primary-50);
    color: var(--primary-600);
    border-color: var(--primary-600);
}

.dark-mode .sidebar-link:hover,
.dark-mode .sidebar-item.active .sidebar-link {
    background: var(--primary-900);
    color: var(--primary-300);
}

.sidebar-link i {
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.sidebar-footer {
    padding: 1rem 0;
    border-top: 1px solid var(--border-color);
}

/* Main Content */
.dashboard-main {
    flex: 1;
    margin-right: 280px;
    padding: 2rem;
    min-height: calc(100vh - 80px);
}

[dir="rtl"] .dashboard-main {
    margin-right: 0;
    margin-left: 280px;
}

/* Dashboard Sections */
.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.section-header p {
    color: var(--text-secondary);
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color-light);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600), var(--primary-700));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    background: var(--primary-100);
    color: var(--primary-600);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.dark-mode .stat-icon {
    background: var(--primary-900);
    color: var(--primary-300);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0 0 0.5rem 0;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
}

.stat-change.positive {
    background: var(--success-100);
    color: var(--success-700);
}

.stat-change.negative {
    background: var(--error-100);
    color: var(--error-700);
}

.stat-change.neutral {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color-light);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
}

.dashboard-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-2xl);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.btn-icon:hover {
    background: var(--bg-secondary);
    color: var(--primary-600);
}

.view-all-link {
    color: var(--primary-600);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.card-content {
    padding: 1.5rem;
}

/* Chart Placeholder */
.chart-placeholder {
    text-align: center;
    padding: 3rem;
    color: var(--text-tertiary);
}

.chart-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

/* Recent Orders */
.recent-orders {
    space-y: 1rem;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color-light);
}

.order-item:last-child {
    border-bottom: none;
}

.order-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.order-info p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.order-amount {
    font-weight: 600;
    color: var(--primary-600);
}

/* Data Table */
.products-table-container {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color-light);
    overflow: hidden;
    position: relative;
}

.products-table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600), var(--primary-700));
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color-light);
}

[dir="ltr"] .data-table th,
[dir="ltr"] .data-table td {
    text-align: left;
}

.data-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.data-table tr:hover {
    background: var(--bg-secondary);
}

.product-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.product-image {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius);
    object-fit: cover;
}

.product-details h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.product-details p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.active {
    background: var(--success-100);
    color: var(--success-700);
}

.status-badge.inactive {
    background: var(--error-100);
    color: var(--error-700);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.action-btn:hover {
    background: var(--bg-secondary);
}

.action-btn.edit:hover {
    color: var(--primary-600);
}

.action-btn.delete:hover {
    color: var(--error-600);
}

/* Coming Soon */
.coming-soon {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-tertiary);
}

.coming-soon i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.coming-soon h3 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-header-content {
        padding: 0 1rem;
        gap: 1rem;
    }

    .header-title h1 {
        font-size: 1.2rem;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .dashboard-sidebar {
        transform: translateX(-100%);
        width: 100%;
        max-width: 280px;
    }

    [dir="rtl"] .dashboard-sidebar {
        transform: translateX(100%);
    }

    .dashboard-sidebar.active {
        transform: translateX(0);
    }

    .dashboard-main {
        margin-right: 0;
        margin-left: 0;
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.75rem 0.5rem;
    }

    .product-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .notification-menu,
    .user-menu {
        position: fixed;
        top: 80px;
        right: 1rem;
        left: 1rem;
        width: auto;
    }
}
