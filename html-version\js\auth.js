// نظام المصادقة وإدارة المستخدمين
class AuthSystem {
    constructor() {
        this.users = [
            {
                id: 1,
                email: '<EMAIL>',
                password: 'admin123',
                name: 'مدير النظام',
                role: 'admin',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
            },
            {
                id: 2,
                email: '<EMAIL>',
                password: 'user123',
                name: 'مستخدم عادي',
                role: 'user',
                avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face'
            }
        ];
        
        this.currentUser = null;
        this.init();
    }

    init() {
        // التحقق من وجود جلسة مفتوحة
        this.checkSession();
        
        // إعداد مستمعي الأحداث
        this.setupEventListeners();
        
        // التحقق من الصفحة الحالية
        this.checkPageAccess();
    }

    setupEventListeners() {
        // نموذج تسجيل الدخول
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // زر إظهار/إخفاء كلمة المرور
        const passwordToggle = document.getElementById('password-toggle');
        if (passwordToggle) {
            passwordToggle.addEventListener('click', this.togglePassword);
        }

        // زر تسجيل الخروج
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }
    }

    async handleLogin() {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const remember = document.getElementById('remember').checked;

        if (!email || !password) {
            this.showNotification('يرجى ملء جميع الحقول', 'error');
            return;
        }

        this.showLoading();

        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1500));

        const user = this.users.find(u => u.email === email && u.password === password);

        if (user) {
            this.currentUser = user;
            
            // حفظ الجلسة
            const sessionData = {
                user: user,
                timestamp: Date.now(),
                remember: remember
            };
            
            if (remember) {
                localStorage.setItem('tab3h_session', JSON.stringify(sessionData));
            } else {
                sessionStorage.setItem('tab3h_session', JSON.stringify(sessionData));
            }

            this.hideLoading();
            this.showNotification(`مرحباً ${user.name}! جاري التوجه إلى لوحة التحكم...`, 'success');
            
            // التوجه إلى لوحة التحكم
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1500);
        } else {
            this.hideLoading();
            this.showNotification('بيانات الدخول غير صحيحة', 'error');
        }
    }

    checkSession() {
        const sessionData = localStorage.getItem('tab3h_session') || 
                           sessionStorage.getItem('tab3h_session');
        
        if (sessionData) {
            try {
                const session = JSON.parse(sessionData);
                const now = Date.now();
                const sessionAge = now - session.timestamp;
                
                // انتهاء صلاحية الجلسة بعد 24 ساعة
                if (sessionAge < 24 * 60 * 60 * 1000) {
                    this.currentUser = session.user;
                    return true;
                } else {
                    this.clearSession();
                }
            } catch (error) {
                this.clearSession();
            }
        }
        return false;
    }

    checkPageAccess() {
        const currentPage = window.location.pathname.split('/').pop();
        const isLoggedIn = this.currentUser !== null;
        
        // الصفحات التي تتطلب تسجيل دخول
        const protectedPages = ['dashboard.html'];
        
        // الصفحات التي لا يجب الوصول إليها عند تسجيل الدخول
        const authPages = ['login.html'];
        
        if (protectedPages.includes(currentPage) && !isLoggedIn) {
            // توجيه إلى صفحة تسجيل الدخول
            window.location.href = 'login.html';
        } else if (authPages.includes(currentPage) && isLoggedIn) {
            // توجيه إلى لوحة التحكم
            window.location.href = 'dashboard.html';
        }
    }

    logout() {
        this.clearSession();
        this.currentUser = null;
        this.showNotification('تم تسجيل الخروج بنجاح', 'success');
        
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1000);
    }

    clearSession() {
        localStorage.removeItem('tab3h_session');
        sessionStorage.removeItem('tab3h_session');
    }

    togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#password-toggle i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }

    showLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.remove('hidden');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        if (!notification) return;

        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');

        // تحديد الأيقونة حسب النوع
        let iconClass = 'fas fa-info-circle';
        if (type === 'success') iconClass = 'fas fa-check-circle';
        if (type === 'error') iconClass = 'fas fa-exclamation-circle';
        if (type === 'warning') iconClass = 'fas fa-exclamation-triangle';

        icon.className = `notification-icon ${iconClass}`;
        messageEl.textContent = message;
        
        // إزالة الفئات السابقة وإضافة الجديدة
        notification.className = `notification ${type}`;
        
        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // إخفاء الإشعار بعد 4 ثوان
        setTimeout(() => {
            notification.classList.remove('show');
        }, 4000);
    }

    getCurrentUser() {
        return this.currentUser;
    }

    isLoggedIn() {
        return this.currentUser !== null;
    }

    hasRole(role) {
        return this.currentUser && this.currentUser.role === role;
    }

    isAdmin() {
        return this.hasRole('admin');
    }
}

// إنشاء مثيل من نظام المصادقة
const authSystem = new AuthSystem();

// تصدير للاستخدام في ملفات أخرى
window.authSystem = authSystem;

// دوال مساعدة للاستخدام العام
function getCurrentUser() {
    return authSystem.getCurrentUser();
}

function isLoggedIn() {
    return authSystem.isLoggedIn();
}

function isAdmin() {
    return authSystem.isAdmin();
}

function logout() {
    authSystem.logout();
}

// التحقق من تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للنموذج
    const formInputs = document.querySelectorAll('.form-input');
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // التحقق من القيم المحفوظة
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
    });

    // تحسين تجربة المستخدم للنموذج
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        // ملء البيانات التجريبية عند النقر
        const demoItems = document.querySelectorAll('.demo-item');
        demoItems.forEach(item => {
            item.addEventListener('click', function() {
                const text = this.textContent;
                if (text.includes('<EMAIL>')) {
                    document.getElementById('email').value = '<EMAIL>';
                    document.getElementById('password').value = 'admin123';
                } else if (text.includes('<EMAIL>')) {
                    document.getElementById('email').value = '<EMAIL>';
                    document.getElementById('password').value = 'user123';
                }
                
                // تحديث حالة الحقول
                formInputs.forEach(input => {
                    if (input.value) {
                        input.parentElement.classList.add('focused');
                    }
                });
            });
        });
    }
});
