/* ===================================
   Tab3h - ملف CSS الرئيسي الموحد
   نسخة محسنة ومنظمة
   =================================== */

/* ===== متغيرات CSS ===== */
:root {
    /* الألوان الأساسية */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* ألوان النص */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;

    /* ألوان الخلفية */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;

    /* الحدود والظلال */
    --border-color: #e5e7eb;
    --border-color-light: #f3f4f6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --shadow-color-dark: rgba(0, 0, 0, 0.2);

    /* المسافات */
    --container-max-width: 1200px;
    --border-radius: 12px;
    --border-radius-lg: 20px;

    /* الانتقالات */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
}

/* الوضع الليلي */
[data-theme="dark"] {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --border-color: #374151;
    --border-color-light: #4b5563;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --shadow-color-dark: rgba(0, 0, 0, 0.5);
}

/* ===== إعادة تعيين الأساسيات ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    font-weight: 400;
    transition: var(--transition);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* خطوط متخصصة للغات */
[lang="ar"] body, [dir="rtl"] body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
}

[lang="en"] body, [dir="ltr"] body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* ===== العناصر الأساسية ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--primary-600);
    border: 2px solid var(--primary-600);
}

.btn-outline:hover {
    background: var(--primary-600);
    color: white;
}

/* ===== الهيدر ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color-light);
    z-index: 1000;
    transition: var(--transition);
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-600);
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
}

/* القائمة */
.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-600);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-600);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

/* أدوات التحكم */
.nav-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.control-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}

/* ===== القسم الرئيسي ===== */
main {
    margin-top: 80px;
}

.hero {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
    overflow: hidden;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--text-primary), var(--primary-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

/* الإحصائيات */
.hero-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-600);
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

/* صورة البطل */
.hero-image {
    position: relative;
}

.hero-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 20px 40px var(--shadow-color);
}

/* ===== الأقسام ===== */
.section {
    padding: 4rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== الشبكات ===== */
.grid {
    display: grid;
    gap: 2rem;
}

.grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
    grid-template-columns: repeat(4, 1fr);
}

/* ===== البطاقات ===== */
.card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow-color);
    border: 1px solid var(--border-color-light);
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px var(--shadow-color-dark);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.card-description {
    color: var(--text-secondary);
    line-height: 1.6;
    flex-grow: 1;
}

/* ===== الاستجابة ===== */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .grid-4 {
        grid-template-columns: repeat(3, 1fr);
    }

    .hero-stats {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .nav-menu {
        display: none;
    }

    .hero {
        padding: 2rem 0;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .grid-3,
    .grid-4 {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-stats {
        gap: 1rem;
    }

    .section {
        padding: 2rem 0;
    }
}

@media (max-width: 480px) {
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .card {
        padding: 1.5rem;
    }
}

/* ===== الرسوم المتحركة ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== المساعدات ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }

/* ===== بطاقات المنتجات والأعمال ===== */
.products-grid,
.works-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    padding: 1rem 0;
}

.product-card,
.work-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 4px 20px var(--shadow-color);
    transition: var(--transition);
    position: relative;
    border: 1px solid var(--border-color-light);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover,
.work-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px var(--shadow-color-dark);
    border-color: var(--primary-300);
}

.product-image,
.work-image {
    position: relative;
    height: 220px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.product-image img,
.work-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img,
.work-card:hover .work-image img {
    transform: scale(1.08);
}

.product-badge,
.work-badge {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    padding: 0.25rem 0.625rem;
    border-radius: 16px;
    font-size: 0.7rem;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.product-info,
.work-info {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.75rem;
}

.product-title,
.work-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    line-height: 1.3;
}

.product-description,
.work-description {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
    flex-grow: 1;
}

.product-footer,
.work-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.5rem;
}

.product-price,
.work-year {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-600);
}

.add-to-cart,
.view-work-btn {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
}

.add-to-cart:hover,
.view-work-btn:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ===== البحث ===== */
.search-container {
    max-width: 600px;
    margin: 0 auto 2rem;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
    position: absolute;
    right: 1rem;
    color: var(--text-tertiary);
}

.clear-search {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition);
}

.clear-search:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* ===== استجابة البطاقات ===== */
@media (max-width: 1200px) {
    .products-grid,
    .works-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .product-image,
    .work-image {
        height: 200px;
    }

    .product-info,
    .work-info {
        padding: 1.25rem;
        gap: 0.5rem;
    }

    .product-title,
    .work-title {
        font-size: 1rem;
    }

    .add-to-cart,
    .view-work-btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
        gap: 0.25rem;
    }
}

@media (max-width: 768px) {
    .products-grid,
    .works-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .product-image,
    .work-image {
        height: 180px;
    }

    .product-info,
    .work-info {
        padding: 1rem;
        gap: 0.5rem;
    }

    .product-title,
    .work-title {
        font-size: 0.95rem;
    }

    .add-to-cart,
    .view-work-btn {
        padding: 0.5rem 0.875rem;
        font-size: 0.7rem;
        gap: 0.25rem;
    }
}

@media (max-width: 480px) {
    .products-grid,
    .works-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0.5rem 0;
    }

    .product-image,
    .work-image {
        height: 200px;
    }

    .product-info,
    .work-info {
        padding: 1.25rem;
        gap: 0.75rem;
    }

    .product-title,
    .work-title {
        font-size: 1.1rem;
    }

    .add-to-cart,
    .view-work-btn {
        padding: 0.625rem 1rem;
        font-size: 0.8rem;
        gap: 0.375rem;
    }
}
