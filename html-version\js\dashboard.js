// لوحة التحكم - وظائف متقدمة
class Dashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.recentOrders = [
            { id: 1, customer: 'أحمد محمد', amount: 150, date: '2024-01-15' },
            { id: 2, customer: 'فاطمة علي', amount: 89, date: '2024-01-14' },
            { id: 3, customer: 'محمد حسن', amount: 220, date: '2024-01-13' },
            { id: 4, customer: 'سارة أحمد', amount: 75, date: '2024-01-12' },
            { id: 5, customer: 'علي محمود', amount: 180, date: '2024-01-11' }
        ];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadUserInfo();
        this.renderRecentOrders();
        this.renderProductsTable();
        this.updateStats();
    }

    setupEventListeners() {
        // Navigation
        const sidebarLinks = document.querySelectorAll('.sidebar-link[data-section]');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('data-section');
                this.switchSection(section);
            });
        });

        // Dropdowns
        this.setupDropdowns();

        // Mobile menu
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        if (mobileToggle) {
            mobileToggle.addEventListener('click', () => {
                this.toggleMobileMenu();
            });
        }

        // Add product button
        const addProductBtn = document.getElementById('add-product-btn');
        if (addProductBtn) {
            addProductBtn.addEventListener('click', () => {
                this.showAddProductModal();
            });
        }
    }

    setupDropdowns() {
        // Notification dropdown
        const notificationBtn = document.getElementById('notification-btn');
        const notificationMenu = document.getElementById('notification-menu');
        
        if (notificationBtn && notificationMenu) {
            notificationBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                notificationMenu.classList.toggle('active');
                
                // Close user menu if open
                const userMenu = document.getElementById('user-menu');
                if (userMenu) userMenu.classList.remove('active');
            });
        }

        // User dropdown
        const userBtn = document.getElementById('user-btn');
        const userMenu = document.getElementById('user-menu');
        
        if (userBtn && userMenu) {
            userBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                userMenu.classList.toggle('active');
                
                // Close notification menu if open
                if (notificationMenu) notificationMenu.classList.remove('active');
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', () => {
            if (notificationMenu) notificationMenu.classList.remove('active');
            if (userMenu) userMenu.classList.remove('active');
        });
    }

    loadUserInfo() {
        const user = getCurrentUser();
        if (!user) return;

        // Update user avatar and name
        const userAvatar = document.getElementById('user-avatar');
        const userAvatarLarge = document.getElementById('user-avatar-large');
        const userName = document.getElementById('user-name');
        const userNameFull = document.getElementById('user-name-full');
        const userEmail = document.getElementById('user-email');

        if (userAvatar) userAvatar.src = user.avatar;
        if (userAvatarLarge) userAvatarLarge.src = user.avatar;
        if (userName) userName.textContent = user.name;
        if (userNameFull) userNameFull.textContent = user.name;
        if (userEmail) userEmail.textContent = user.email;
    }

    switchSection(sectionName) {
        // Hide all sections
        const sections = document.querySelectorAll('.dashboard-section');
        sections.forEach(section => section.classList.remove('active'));

        // Show target section
        const targetSection = document.getElementById(`section-${sectionName}`);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // Update sidebar active state
        const sidebarItems = document.querySelectorAll('.sidebar-item');
        sidebarItems.forEach(item => item.classList.remove('active'));

        const activeSidebarItem = document.querySelector(`[data-section="${sectionName}"]`)?.closest('.sidebar-item');
        if (activeSidebarItem) {
            activeSidebarItem.classList.add('active');
        }

        this.currentSection = sectionName;

        // Load section-specific data
        this.loadSectionData(sectionName);
    }

    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'products':
                this.renderProductsTable();
                break;
            case 'orders':
                // Load orders data
                break;
            case 'customers':
                // Load customers data
                break;
            case 'analytics':
                // Load analytics data
                break;
            case 'settings':
                // Load settings
                break;
        }
    }

    renderRecentOrders() {
        const container = document.getElementById('recent-orders');
        if (!container) return;

        const currentLang = getCurrentLanguage();
        
        container.innerHTML = this.recentOrders.map(order => `
            <div class="order-item">
                <div class="order-info">
                    <h4>${currentLang === 'ar' ? 'طلب رقم' : currentLang === 'en' ? 'Order' : 'Sipariş'} #${order.id}</h4>
                    <p>${order.customer} - ${order.date}</p>
                </div>
                <div class="order-amount">$${order.amount}</div>
            </div>
        `).join('');
    }

    renderProductsTable() {
        const tableBody = document.getElementById('products-table-body');
        if (!tableBody) return;

        const currentLang = getCurrentLanguage();
        
        tableBody.innerHTML = products.map(product => `
            <tr>
                <td>
                    <div class="product-info">
                        <img src="${product.image}" alt="${product.name[currentLang]}" class="product-image">
                        <div class="product-details">
                            <h4>${product.name[currentLang]}</h4>
                            <p>${product.description[currentLang].substring(0, 50)}...</p>
                        </div>
                    </div>
                </td>
                <td>${product.category[currentLang]}</td>
                <td>$${product.price}</td>
                <td>
                    <span class="status-badge active">
                        ${currentLang === 'ar' ? 'نشط' : currentLang === 'en' ? 'Active' : 'Aktif'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="action-btn edit" onclick="dashboard.editProduct(${product.id})" title="${currentLang === 'ar' ? 'تعديل' : currentLang === 'en' ? 'Edit' : 'Düzenle'}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="action-btn delete" onclick="dashboard.deleteProduct(${product.id})" title="${currentLang === 'ar' ? 'حذف' : currentLang === 'en' ? 'Delete' : 'Sil'}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    updateStats() {
        // Update stats with real data
        const totalOrders = this.recentOrders.length;
        const totalRevenue = this.recentOrders.reduce((sum, order) => sum + order.amount, 0);
        const totalCustomers = new Set(this.recentOrders.map(order => order.customer)).size;
        const totalProducts = products.length;

        // Update DOM elements
        const statsElements = {
            orders: document.querySelector('.stat-card:nth-child(1) .stat-number'),
            revenue: document.querySelector('.stat-card:nth-child(2) .stat-number'),
            customers: document.querySelector('.stat-card:nth-child(3) .stat-number'),
            products: document.querySelector('.stat-card:nth-child(4) .stat-number')
        };

        if (statsElements.orders) statsElements.orders.textContent = totalOrders;
        if (statsElements.revenue) statsElements.revenue.textContent = `$${totalRevenue.toLocaleString()}`;
        if (statsElements.customers) statsElements.customers.textContent = totalCustomers;
        if (statsElements.products) statsElements.products.textContent = totalProducts;
    }

    toggleMobileMenu() {
        const sidebar = document.getElementById('dashboard-sidebar');
        if (sidebar) {
            sidebar.classList.toggle('active');
        }
    }

    editProduct(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        const currentLang = getCurrentLanguage();
        const message = currentLang === 'ar' ? 
            `تعديل المنتج: ${product.name[currentLang]}` :
            currentLang === 'en' ? 
            `Edit Product: ${product.name[currentLang]}` :
            `Ürünü Düzenle: ${product.name[currentLang]}`;

        alert(message + '\n' + (currentLang === 'ar' ? 'هذه الميزة قيد التطوير' : currentLang === 'en' ? 'This feature is under development' : 'Bu özellik geliştiriliyor'));
    }

    deleteProduct(productId) {
        const product = products.find(p => p.id === productId);
        if (!product) return;

        const currentLang = getCurrentLanguage();
        const confirmMessage = currentLang === 'ar' ? 
            `هل أنت متأكد من حذف المنتج: ${product.name[currentLang]}؟` :
            currentLang === 'en' ? 
            `Are you sure you want to delete: ${product.name[currentLang]}?` :
            `Silmek istediğinizden emin misiniz: ${product.name[currentLang]}?`;

        if (confirm(confirmMessage)) {
            // Remove product from array
            const index = products.findIndex(p => p.id === productId);
            if (index > -1) {
                products.splice(index, 1);
                this.renderProductsTable();
                this.updateStats();
                
                const successMessage = currentLang === 'ar' ? 
                    'تم حذف المنتج بنجاح' :
                    currentLang === 'en' ? 
                    'Product deleted successfully' :
                    'Ürün başarıyla silindi';
                
                this.showNotification(successMessage, 'success');
            }
        }
    }

    showAddProductModal() {
        const currentLang = getCurrentLanguage();
        const message = currentLang === 'ar' ? 
            'إضافة منتج جديد' :
            currentLang === 'en' ? 
            'Add New Product' :
            'Yeni Ürün Ekle';

        alert(message + '\n' + (currentLang === 'ar' ? 'هذه الميزة قيد التطوير' : currentLang === 'en' ? 'This feature is under development' : 'Bu özellik geliştiriliyor'));
    }

    showNotification(message, type = 'info') {
        // Create notification element if it doesn't exist
        let notification = document.getElementById('dashboard-notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'dashboard-notification';
            notification.className = 'notification hidden';
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="notification-icon"></i>
                    <span class="notification-message"></span>
                </div>
            `;
            document.body.appendChild(notification);
        }

        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');

        // Set icon based on type
        let iconClass = 'fas fa-info-circle';
        if (type === 'success') iconClass = 'fas fa-check-circle';
        if (type === 'error') iconClass = 'fas fa-exclamation-circle';
        if (type === 'warning') iconClass = 'fas fa-exclamation-triangle';

        icon.className = `notification-icon ${iconClass}`;
        messageEl.textContent = message;
        
        // Set notification type
        notification.className = `notification ${type}`;
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Hide notification after 4 seconds
        setTimeout(() => {
            notification.classList.remove('show');
        }, 4000);
    }

    // Method to refresh dashboard data
    refreshData() {
        this.renderRecentOrders();
        this.renderProductsTable();
        this.updateStats();
    }
}

// Initialize dashboard when DOM is loaded
let dashboard;

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the dashboard page
    if (document.querySelector('.dashboard-page')) {
        dashboard = new Dashboard();
        
        // Auto-refresh data every 5 minutes
        setInterval(() => {
            dashboard.refreshData();
        }, 5 * 60 * 1000);
    }
});

// Export for global access
window.dashboard = dashboard;
