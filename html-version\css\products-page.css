/* ===== صفحة المنتجات - تنسيق احترافي ===== */

/* تأكد من تطبيق تنسيقات الهيدر */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color-light);
    z-index: 1000;
    transition: var(--transition);
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-600);
}

.logo-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: inherit;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    padding: 0.5rem 0;
}

.nav-link:hover {
    color: var(--primary-600);
}

.nav-link.active {
    color: var(--primary-600);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-600);
    transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-btn, .theme-toggle, .mobile-toggle {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.cart-btn:hover, .theme-toggle:hover, .mobile-toggle:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--primary-600);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-count:empty {
    display: none;
}

.language-selector {
    position: relative;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--bg-secondary);
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    color: var(--text-primary);
}

.lang-btn:hover {
    background: var(--primary-600);
    color: white;
}

.lang-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    box-shadow: 0 4px 20px var(--shadow-color);
    min-width: 150px;
    z-index: 1000;
    display: none;
}

.lang-menu.active {
    display: block;
}

.lang-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    color: var(--text-primary);
}

.lang-option:hover {
    background: var(--bg-secondary);
}

.mobile-toggle {
    display: none;
    flex-direction: column;
    gap: 3px;
}

.mobile-toggle span {
    width: 20px;
    height: 2px;
    background: currentColor;
    transition: var(--transition);
}

/* Main Content */
.main-content {
    padding-top: 80px;
    min-height: 100vh;
    background: var(--bg-primary);
}

/* استجابة الهيدر */
@media (max-width: 768px) {
    .mobile-toggle {
        display: flex;
    }

    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: 0 0 10px 10px;
        box-shadow: 0 4px 20px var(--shadow-color);
        flex-direction: column;
        gap: 0;
        display: none;
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-link {
        padding: 1rem;
        border-bottom: 1px solid var(--border-color-light);
    }

    .nav-link:last-child {
        border-bottom: none;
    }
}

/* ===== Products Hero Section ===== */
.products-hero {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 50%, var(--primary-800) 100%);
    color: white;
    padding: 6rem 0 4rem;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.products-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    opacity: 0.9;
}

.breadcrumb a {
    color: white;
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb a:hover {
    opacity: 0.8;
}

.breadcrumb i {
    font-size: 0.8rem;
    opacity: 0.7;
}

.page-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Filters Section */
.filters-section {
    background: var(--bg-secondary);
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 80px;
    z-index: 100;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.filters-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Search Container */
.search-container {
    display: flex;
    justify-content: center;
}

.search-input-wrapper {
    position: relative;
    max-width: 500px;
    width: 100%;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    z-index: 1;
}

[dir="rtl"] .search-icon {
    left: auto;
    right: 1rem;
}

.search-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
}

[dir="rtl"] .search-input {
    padding: 1rem 3rem 1rem 1rem;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.clear-search {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition-fast);
}

[dir="rtl"] .clear-search {
    right: auto;
    left: 1rem;
}

.clear-search:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Filter Controls */
.filter-controls {
    display: flex;
    align-items: end;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* View Toggle */
.view-toggle {
    display: flex;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--bg-primary);
}

.view-btn {
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn.active,
.view-btn:hover {
    background: var(--primary-600);
    color: white;
}

/* Active Filters */
.active-filters {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.filters-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filters-list {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-tag {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--primary-100);
    color: var(--primary-800);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
}

.dark-mode .filter-tag {
    background: var(--primary-900);
    color: var(--primary-200);
}

.filter-tag button {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.2rem;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.filter-tag button:hover {
    background: rgba(0, 0, 0, 0.1);
}

.clear-all-btn {
    padding: 0.5rem 1rem;
    background: var(--error-500);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.clear-all-btn:hover {
    background: var(--error-600);
}

/* Products Section */
.products-section {
    padding: 3rem 0;
    background: var(--bg-primary);
}

/* Results Info */
.results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.results-count {
    font-weight: 500;
    color: var(--text-primary);
}

.loading-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== Products Grid ===== */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    padding: 1rem 0;
}

.products-grid.list-view {
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

.products-grid.list-view .product-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
}

.products-grid.list-view .product-image {
    width: 150px;
    height: 150px;
    flex-shrink: 0;
    margin-left: 1.5rem;
}

[dir="rtl"] .products-grid.list-view .product-image {
    margin-left: 0;
    margin-right: 1.5rem;
}

.products-grid.list-view .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 150px;
}

.products-grid.list-view .product-footer {
    margin-top: auto;
}

/* ===== Enhanced Product Card - متطابق مع الموقع الرئيسي ===== */
.product-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 4px 20px var(--shadow-color);
    transition: var(--transition);
    position: relative;
    border: 1px solid var(--border-color-light);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px var(--shadow-color-dark);
    border-color: var(--primary-300);
}

.product-image {
    position: relative;
    height: 220px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.08);
}

.product-badge {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    padding: 0.25rem 0.625rem;
    border-radius: 16px;
    font-size: 0.7rem;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.product-info {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.75rem;
}

.product-category {
    color: var(--primary-600);
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    line-height: 1.3;
}

.product-description {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
    flex-grow: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.5rem;
}

.product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-600);
}

.add-to-cart {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
}

.add-to-cart:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.add-to-cart:active {
    transform: translateY(0);
}

/* No Results */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
}

.no-results-icon {
    font-size: 4rem;
    color: var(--text-tertiary);
    margin-bottom: 1.5rem;
}

.no-results-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.no-results-text {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* Load More */
.load-more-container {
    text-align: center;
    margin-top: 3rem;
}

/* Loading Spinner */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Responsive Design - متطابق مع الموقع الرئيسي ===== */
@media (max-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .product-image {
        height: 200px;
    }

    .product-info {
        padding: 1.25rem;
        gap: 0.5rem;
    }

    .product-title {
        font-size: 1rem;
    }

    .add-to-cart {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
        gap: 0.25rem;
    }
}

@media (max-width: 768px) {
    .products-hero {
        padding: 3rem 0;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
    }

    .hero-stats {
        gap: 2rem;
    }

    .filters-section {
        padding: 1.5rem 0;
        position: static;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .search-input-wrapper {
        max-width: none;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .product-image {
        height: 180px;
    }

    .product-info {
        padding: 1rem;
        gap: 0.5rem;
    }

    .product-title {
        font-size: 0.95rem;
    }

    .add-to-cart {
        padding: 0.5rem 0.875rem;
        font-size: 0.7rem;
        gap: 0.25rem;
    }

    .results-info {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .active-filters {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .filters-list {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .products-hero {
        padding: 2rem 0;
    }

    .page-title {
        font-size: 2rem;
    }

    .page-subtitle {
        font-size: 1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0.5rem 0;
    }

    .product-image {
        height: 200px;
    }

    .product-info {
        padding: 1.25rem;
        gap: 0.75rem;
    }

    .product-title {
        font-size: 1.1rem;
    }

    .product-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .add-to-cart {
        width: 100%;
        justify-content: center;
        padding: 0.625rem 1rem;
        font-size: 0.8rem;
        gap: 0.375rem;
    }

    .no-results {
        padding: 3rem 1rem;
    }

    .no-results-icon {
        font-size: 3rem;
    }

    .no-results-title {
        font-size: 1.25rem;
    }

    .search-input-wrapper {
        max-width: none;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }
}

/* ===== Dark Mode Enhancements - متطابق مع الموقع الرئيسي ===== */
[data-theme="dark"] .products-hero {
    background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 50%, var(--primary-900) 100%);
}

[data-theme="dark"] .filters-section {
    background: var(--bg-tertiary);
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .products-section {
    background: var(--bg-primary);
}

[data-theme="dark"] .results-info {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

[data-theme="dark"] .no-results {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

[data-theme="dark"] .active-filters {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

[data-theme="dark"] .product-card {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .product-card:hover {
    border-color: var(--primary-400);
}

[data-theme="dark"] .product-image {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
}

[data-theme="dark"] .search-input,
[data-theme="dark"] .filter-select {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .search-input:focus,
[data-theme="dark"] .filter-select:focus {
    border-color: var(--primary-500);
}

[data-theme="dark"] .view-toggle {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

[data-theme="dark"] .loading-spinner {
    border-color: var(--border-color);
    border-top-color: var(--primary-500);
}

/* تحسين الوضع الليلي للهيدر */
[data-theme="dark"] .header {
    background: rgba(17, 24, 39, 0.95);
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .logo {
    color: var(--primary-400);
}

[data-theme="dark"] .nav-link {
    color: var(--text-primary);
}

[data-theme="dark"] .nav-link:hover,
[data-theme="dark"] .nav-link.active {
    color: var(--primary-400);
}

[data-theme="dark"] .cart-btn,
[data-theme="dark"] .theme-toggle,
[data-theme="dark"] .mobile-toggle,
[data-theme="dark"] .lang-btn {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-theme="dark"] .cart-btn:hover,
[data-theme="dark"] .theme-toggle:hover,
[data-theme="dark"] .mobile-toggle:hover,
[data-theme="dark"] .lang-btn:hover {
    background: var(--primary-600);
    color: white;
}

[data-theme="dark"] .lang-menu {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .lang-option:hover {
    background: var(--bg-tertiary);
}

[data-theme="dark"] .nav-menu {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

/* ===== تحسينات البحث المتقدم ===== */

/* تمييز النص في نتائج البحث */
.search-highlight {
    background: linear-gradient(135deg, #fef3c7, #fbbf24);
    color: #92400e;
    padding: 0.125rem 0.25rem;
    border-radius: 4px;
    font-weight: 600;
}

[data-theme="dark"] .search-highlight {
    background: linear-gradient(135deg, #451a03, #92400e);
    color: #fbbf24;
}

/* اقتراحات البحث */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    box-shadow: 0 8px 25px var(--shadow-color);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color-light);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.active {
    background: var(--bg-secondary);
}

.suggestion-item i {
    color: var(--text-tertiary);
    font-size: 0.875rem;
}

.suggestion-item span {
    color: var(--text-primary);
    font-size: 0.875rem;
}

.suggestion-item strong {
    color: var(--primary-600);
    font-weight: 600;
}

/* زر البحث الصوتي */
.voice-search-btn {
    position: absolute;
    right: 3rem;
    top: 50%;
    transform: translateY(-50%);
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.voice-search-btn:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-50%) scale(1.05);
}

.voice-search-btn.listening {
    background: #ef4444;
    color: white;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: translateY(-50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-50%) scale(1.1);
        opacity: 0.8;
    }
}

/* تحسين شريط البحث */
.search-input-wrapper {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-input {
    width: 100%;
    padding: 1rem 4.5rem 1rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: var(--transition);
    box-shadow: 0 2px 8px var(--shadow-color);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
    color: var(--text-tertiary);
}

/* زر مسح البحث */
.clear-search {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    border: none;
    background: var(--text-tertiary);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.clear-search:hover {
    background: #ef4444;
    transform: translateY(-50%) scale(1.1);
}

.clear-search.hidden {
    display: none;
}

/* مؤشر التحميل */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 2rem;
    color: var(--text-secondary);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* رسالة عدم وجود نتائج */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    margin: 2rem 0;
}

.no-results-icon {
    font-size: 4rem;
    color: var(--text-tertiary);
    margin-bottom: 1.5rem;
}

.no-results-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.no-results-text {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
}

.no-results-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1.5rem;
}

.suggestion-tag {
    background: var(--primary-100);
    color: var(--primary-700);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
}

.suggestion-tag:hover {
    background: var(--primary-600);
    color: white;
}

/* الوضع الليلي للبحث */
[data-theme="dark"] .search-suggestions {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .suggestion-item:hover,
[data-theme="dark"] .suggestion-item.active {
    background: var(--bg-tertiary);
}

[data-theme="dark"] .voice-search-btn {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-theme="dark"] .voice-search-btn:hover {
    background: var(--primary-600);
    color: white;
}

[data-theme="dark"] .search-input {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .search-input:focus {
    border-color: var(--primary-500);
}

[data-theme="dark"] .no-results {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

[data-theme="dark"] .suggestion-tag {
    background: var(--primary-900);
    color: var(--primary-300);
}

[data-theme="dark"] .suggestion-tag:hover {
    background: var(--primary-600);
    color: white;
}

/* استجابة البحث */
@media (max-width: 768px) {
    .search-input-wrapper {
        max-width: none;
    }

    .search-input {
        padding: 0.875rem 4rem 0.875rem 0.875rem;
        font-size: 0.875rem;
    }

    .voice-search-btn {
        right: 2.5rem;
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    .clear-search {
        right: 0.5rem;
        width: 20px;
        height: 20px;
        font-size: 0.625rem;
    }

    .no-results {
        padding: 3rem 1.5rem;
    }

    .no-results-icon {
        font-size: 3rem;
    }

    .no-results-title {
        font-size: 1.25rem;
    }

    .no-results-text {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .search-input {
        padding: 0.75rem 3.5rem 0.75rem 0.75rem;
        font-size: 0.875rem;
    }

    .voice-search-btn {
        right: 2rem;
        width: 24px;
        height: 24px;
        font-size: 0.625rem;
    }

    .no-results-suggestions {
        flex-direction: column;
        align-items: center;
    }

    .suggestion-tag {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.fade-in.delay-1 { animation-delay: 0.1s; }
.fade-in.delay-2 { animation-delay: 0.2s; }
.fade-in.delay-3 { animation-delay: 0.3s; }
.fade-in.delay-4 { animation-delay: 0.4s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }

.add-to-cart:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* No Results */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 2px dashed var(--border-color);
}

.no-results-icon {
    font-size: 4rem;
    color: var(--text-tertiary);
    margin-bottom: 1.5rem;
}

.no-results-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.no-results-text {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* Load More */
.load-more-container {
    text-align: center;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .products-hero {
        padding: 2rem 0;
    }

    .page-title {
        font-size: 2rem;
    }

    .hero-stats {
        gap: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .filters-section {
        position: static;
        padding: 1.5rem 0;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .view-toggle {
        align-self: center;
        width: fit-content;
    }

    .active-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .results-info {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .products-grid.list-view .product-card {
        flex-direction: column;
        text-align: center;
    }

    .products-grid.list-view .product-image {
        width: 100%;
        height: 200px;
        margin: 0 0 1rem 0;
    }

    .products-grid.list-view .product-info {
        height: auto;
    }

    .product-footer {
        flex-direction: column;
        gap: 1rem;
    }

    .add-to-cart {
        width: 100%;
        justify-content: center;
    }
}
