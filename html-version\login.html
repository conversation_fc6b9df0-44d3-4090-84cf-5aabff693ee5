<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Tab3h</title>
    <meta name="description" content="تسجيل الدخول إلى لوحة التحكم">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/enhancements.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/professional-enhancements.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body class="light-mode auth-page">
    <!-- Header -->
    <header class="header" id="header">
        <div class="container">
            <nav class="navbar">
                <!-- Logo -->
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <div class="logo-icon">T</div>
                        <span class="logo-text">Tab3h</span>
                    </a>
                </div>

                <!-- Controls -->
                <div class="nav-controls">
                    <!-- Language Selector -->
                    <div class="language-selector">
                        <button type="button" class="lang-btn" id="lang-btn" title="تغيير اللغة" aria-label="تغيير اللغة">
                            <i class="fas fa-globe"></i>
                            <span class="current-lang">🇸🇦</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="lang-menu" id="lang-menu">
                            <button type="button" class="lang-option" data-lang="ar">
                                <span class="flag">🇸🇦</span>
                                <span>العربية</span>
                            </button>
                            <button type="button" class="lang-option" data-lang="en">
                                <span class="flag">🇺🇸</span>
                                <span>English</span>
                            </button>
                            <button type="button" class="lang-option" data-lang="tr">
                                <span class="flag">🇹🇷</span>
                                <span>Türkçe</span>
                            </button>
                        </div>
                    </div>

                    <!-- Theme Toggle -->
                    <button type="button" class="theme-toggle" id="theme-toggle" title="تبديل الوضع الليلي" aria-label="تبديل الوضع الليلي">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <!-- Auth Header -->
                <div class="auth-header">
                    <div class="auth-logo">
                        <div class="logo-icon">T</div>
                        <span class="logo-text">Tab3h</span>
                    </div>
                    <h1 class="auth-title" data-key="loginTitle">تسجيل الدخول</h1>
                    <p class="auth-subtitle" data-key="loginSubtitle">أدخل بياناتك للوصول إلى لوحة التحكم</p>
                </div>

                <!-- Login Form -->
                <form class="auth-form" id="login-form">
                    <div class="form-group">
                        <label for="email" class="form-label" data-key="email">البريد الإلكتروني</label>
                        <div class="input-wrapper">
                            <i class="fas fa-envelope input-icon"></i>
                            <input type="email" id="email" name="email" class="form-input" required 
                                   data-key="emailPlaceholder" placeholder="أدخل بريدك الإلكتروني">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label" data-key="password">كلمة المرور</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="password" name="password" class="form-input" required 
                                   data-key="passwordPlaceholder" placeholder="أدخل كلمة المرور">
                            <button type="button" class="password-toggle" id="password-toggle" title="إظهار/إخفاء كلمة المرور">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-wrapper">
                            <input type="checkbox" id="remember" name="remember">
                            <span class="checkmark"></span>
                            <span class="checkbox-label" data-key="rememberMe">تذكرني</span>
                        </label>
                        <a href="#" class="forgot-password" data-key="forgotPassword">نسيت كلمة المرور؟</a>
                    </div>

                    <button type="submit" class="btn btn-gradient auth-submit" id="login-btn">
                        <span data-key="login">تسجيل الدخول</span>
                        <i class="fas fa-arrow-left"></i>
                    </button>

                    <!-- Demo Credentials -->
                    <div class="demo-credentials">
                        <h4 data-key="demoCredentials">بيانات تجريبية:</h4>
                        <div class="demo-item">
                            <strong data-key="adminAccount">حساب المدير:</strong>
                            <span><EMAIL> / admin123</span>
                        </div>
                        <div class="demo-item">
                            <strong data-key="userAccount">حساب المستخدم:</strong>
                            <span><EMAIL> / user123</span>
                        </div>
                    </div>
                </form>

                <!-- Auth Footer -->
                <div class="auth-footer">
                    <p class="auth-footer-text">
                        <span data-key="noAccount">ليس لديك حساب؟</span>
                        <a href="#" class="auth-link" data-key="createAccount">إنشاء حساب جديد</a>
                    </p>
                    <div class="auth-divider">
                        <span data-key="or">أو</span>
                    </div>
                    <a href="index.html" class="btn btn-outline">
                        <i class="fas fa-home"></i>
                        <span data-key="backToHome">العودة للرئيسية</span>
                    </a>
                </div>
            </div>

            <!-- Background Elements -->
            <div class="auth-bg">
                <div class="bg-shape shape-1"></div>
                <div class="bg-shape shape-2"></div>
                <div class="bg-shape shape-3"></div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay hidden" id="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p data-key="authenticating">جاري التحقق من البيانات...</p>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification hidden" id="notification">
        <div class="notification-content">
            <i class="notification-icon"></i>
            <span class="notification-message"></span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/translations.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
