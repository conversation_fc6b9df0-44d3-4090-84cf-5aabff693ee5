// إدارة سلة الشراء
class Cart {
    constructor() {
        this.items = this.loadCart();
        this.updateCartCount();
        this.bindEvents();
    }

    // تحميل السلة من localStorage
    loadCart() {
        const savedCart = localStorage.getItem('cart');
        return savedCart ? JSON.parse(savedCart) : [];
    }

    // حفظ السلة في localStorage
    saveCart() {
        localStorage.setItem('cart', JSON.stringify(this.items));
    }

    // إضافة منتج إلى السلة
    addItem(product) {
        const existingItem = this.items.find(item => item.id === product.id);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.items.push({
                ...product,
                quantity: 1
            });
        }

        this.saveCart();
        this.updateCartCount();
        this.showNotification(t('success'), 'success');
    }

    // حذف منتج من السلة
    removeItem(productId) {
        this.items = this.items.filter(item => item.id !== productId);
        this.saveCart();
        this.updateCartCount();
        this.renderCartItems();
        this.updateCartTotal();
    }

    // تحديث كمية المنتج
    updateQuantity(productId, change) {
        const item = this.items.find(item => item.id === productId);
        if (item) {
            item.quantity += change;
            if (item.quantity <= 0) {
                this.removeItem(productId);
            } else {
                this.saveCart();
                this.renderCartItems();
                this.updateCartTotal();
            }
        }
    }

    // حساب إجمالي السلة
    getTotal() {
        return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    // حساب عدد العناصر في السلة
    getItemCount() {
        return this.items.reduce((count, item) => count + item.quantity, 0);
    }

    // تحديث عداد السلة في الهيدر
    updateCartCount() {
        const cartCountElement = document.getElementById('cart-count');
        if (cartCountElement) {
            const count = this.getItemCount();
            cartCountElement.textContent = count;
            cartCountElement.style.display = count > 0 ? 'flex' : 'none';
        }
    }

    // عرض عناصر السلة في المودال
    renderCartItems() {
        const cartItemsContainer = document.getElementById('cart-items');
        const cartEmptyElement = document.getElementById('cart-empty');
        const cartFooter = document.getElementById('cart-footer');

        if (!cartItemsContainer) return;

        if (this.items.length === 0) {
            cartItemsContainer.innerHTML = '';
            cartEmptyElement.classList.remove('hidden');
            cartFooter.classList.add('hidden');
            return;
        }

        cartEmptyElement.classList.add('hidden');
        cartFooter.classList.remove('hidden');

        const currentLang = getCurrentLanguage();
        cartItemsContainer.innerHTML = this.items.map(item => `
            <div class="cart-item">
                <div class="cart-item-image">
                    <img src="${item.image}" alt="${item.name[currentLang]}" loading="lazy">
                </div>
                <div class="cart-item-info">
                    <h4 class="cart-item-title">${item.name[currentLang]}</h4>
                    <p class="cart-item-category">${item.category[currentLang]}</p>
                    <p class="cart-item-price">$${item.price} × ${item.quantity}</p>
                </div>
                <div class="cart-item-actions">
                    <button class="quantity-btn" onclick="cart.updateQuantity(${item.id}, -1)">
                        <i class="fas fa-minus"></i>
                    </button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn" onclick="cart.updateQuantity(${item.id}, 1)">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="remove-btn" onclick="cart.removeItem(${item.id})" title="${t('removeFromCart')}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    // تحديث إجمالي السلة
    updateCartTotal() {
        const totalElement = document.getElementById('total-amount');
        if (totalElement) {
            totalElement.textContent = `$${this.getTotal().toFixed(2)}`;
        }
    }

    // فتح مودال السلة
    openCartModal() {
        const modal = document.getElementById('cart-modal');
        if (modal) {
            this.renderCartItems();
            this.updateCartTotal();
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    // إغلاق مودال السلة
    closeCartModal() {
        const modal = document.getElementById('cart-modal');
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    // إنشاء رسالة واتساب للطلب
    generateWhatsAppMessage() {
        const currentLang = getCurrentLanguage();
        const orderDetails = this.items.map(item =>
            `${item.name[currentLang]} - ${t('quantity')}: ${item.quantity} - ${t('price')}: $${(item.price * item.quantity).toFixed(2)}`
        ).join('\n');

        const greeting = currentLang === 'ar'
            ? 'مرحباً، أريد طلب المنتجات التالية'
            : currentLang === 'en'
            ? 'Hello, I want to order the following products'
            : 'Merhaba, aşağıdaki ürünleri sipariş etmek istiyorum';

        const totalText = currentLang === 'ar'
            ? 'المجموع الكلي'
            : currentLang === 'en'
            ? 'Total Amount'
            : 'Toplam Tutar';

        const thankYou = currentLang === 'ar'
            ? 'شكراً لكم'
            : currentLang === 'en'
            ? 'Thank you'
            : 'Teşekkürler';

        const message = `${greeting}:

${orderDetails}

${totalText}: $${this.getTotal().toFixed(2)}

${thankYou}`;

        return encodeURIComponent(message);
    }

    // إرسال الطلب عبر واتساب
    sendWhatsAppOrder() {
        if (this.items.length === 0) {
            this.showNotification(t('cartEmpty'), 'error');
            return;
        }

        const message = this.generateWhatsAppMessage();
        const whatsappUrl = `https://wa.me/${contactInfo.whatsapp.replace(/[^0-9]/g, '')}?text=${message}`;
        window.open(whatsappUrl, '_blank');

        // إغلاق المودال بعد الإرسال
        this.closeCartModal();
    }

    // عرض إشعار
    showNotification(message, type = 'info') {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        // إضافة الإشعار إلى الصفحة
        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // ربط الأحداث
    bindEvents() {
        // فتح مودال السلة
        const cartBtn = document.getElementById('cart-btn');
        if (cartBtn) {
            cartBtn.addEventListener('click', () => this.openCartModal());
        }

        // إغلاق مودال السلة
        const cartClose = document.getElementById('cart-close');
        if (cartClose) {
            cartClose.addEventListener('click', () => this.closeCartModal());
        }

        // إغلاق المودال عند النقر خارجه
        const cartModal = document.getElementById('cart-modal');
        if (cartModal) {
            cartModal.addEventListener('click', (e) => {
                if (e.target === cartModal) {
                    this.closeCartModal();
                }
            });
        }

        // إرسال الطلب عبر واتساب
        const whatsappBtn = document.getElementById('whatsapp-order');
        if (whatsappBtn) {
            whatsappBtn.addEventListener('click', () => this.sendWhatsAppOrder());
        }

        // إضافة المنتجات إلى السلة
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('add-to-cart') || e.target.closest('.add-to-cart')) {
                const button = e.target.classList.contains('add-to-cart') ? e.target : e.target.closest('.add-to-cart');
                const productId = parseInt(button.getAttribute('data-product-id'));
                const product = products.find(p => p.id === productId);

                if (product) {
                    this.addItem(product);
                }
            }
        });
    }

    // مسح السلة
    clearCart() {
        this.items = [];
        this.saveCart();
        this.updateCartCount();
        this.renderCartItems();
        this.updateCartTotal();
    }
}

// إنشاء مثيل من السلة
const cart = new Cart();

// إضافة أنماط CSS للإشعارات
const notificationStyles = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        box-shadow: 0 10px 25px var(--shadow-color);
        padding: 1rem;
        z-index: 10000;
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.3s ease;
        max-width: 300px;
    }

    .notification.show {
        transform: translateX(0);
        opacity: 1;
    }

    .notification-success {
        border-left: 4px solid #10b981;
    }

    .notification-error {
        border-left: 4px solid #ef4444;
    }

    .notification-info {
        border-left: 4px solid var(--primary-500);
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-primary);
    }

    .notification-success .notification-content i {
        color: #10b981;
    }

    .notification-error .notification-content i {
        color: #ef4444;
    }

    .notification-info .notification-content i {
        color: var(--primary-500);
    }

    [dir="rtl"] .notification {
        right: auto;
        left: 20px;
        transform: translateX(-100%);
    }

    [dir="rtl"] .notification.show {
        transform: translateX(0);
    }
`;

// إضافة الأنماط إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);
