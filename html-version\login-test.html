<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول - Tab3h</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            background: white;
            color: #333;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .btn {
            width: 100%;
            padding: 1rem;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            margin-top: 1rem;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .demo-box {
            background: #e7f3ff;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            cursor: pointer;
            border: 2px solid #007bff;
        }
        
        .demo-box:hover {
            background: #d1ecf1;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>اختبار تسجيل الدخول</h2>
        
        <div class="demo-box" onclick="fillCredentials()">
            <strong>بيانات الدخول:</strong><br>
            اسم المستخدم: sharabi<br>
            الرمز السري: 4568520<br>
            <small>انقر هنا للملء التلقائي</small>
        </div>
        
        <form id="test-form">
            <div class="form-group">
                <label for="test-username" class="form-label">اسم المستخدم</label>
                <input type="text" id="test-username" name="username" class="form-input" placeholder="أدخل اسم المستخدم">
            </div>
            
            <div class="form-group">
                <label for="test-password" class="form-label">الرمز السري</label>
                <input type="password" id="test-password" name="password" class="form-input" placeholder="أدخل الرمز السري">
            </div>
            
            <button type="submit" class="btn">تسجيل الدخول</button>
        </form>
        
        <div id="result" style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 5px; display: none;"></div>
    </div>

    <script>
        function fillCredentials() {
            document.getElementById('test-username').value = 'sharabi';
            document.getElementById('test-password').value = '4568520';
            console.log('Credentials filled');
        }
        
        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            const result = document.getElementById('result');
            
            console.log('Form submitted:', { username, password });
            
            if (username === 'sharabi' && password === '4568520') {
                result.style.display = 'block';
                result.style.background = '#d4edda';
                result.style.color = '#155724';
                result.innerHTML = '✅ تم تسجيل الدخول بنجاح! سيتم التوجه إلى لوحة التحكم...';
                
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);
            } else {
                result.style.display = 'block';
                result.style.background = '#f8d7da';
                result.style.color = '#721c24';
                result.innerHTML = '❌ بيانات الدخول غير صحيحة';
            }
        });
        
        // Test input events
        document.getElementById('test-username').addEventListener('input', function() {
            console.log('Username input:', this.value);
        });
        
        document.getElementById('test-password').addEventListener('input', function() {
            console.log('Password input:', this.value);
        });
        
        console.log('Test page loaded successfully');
    </script>
</body>
</html>
