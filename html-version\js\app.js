/* ===================================
   Tab3h - ملف JavaScript الرئيسي الموحد
   نسخة محسنة ومنظمة
   =================================== */

// ===== المتغيرات العامة =====
let currentLanguage = 'ar';
let currentTheme = 'light';
let cart = [];

// ===== تهيئة التطبيق =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadData();
    bindEvents();
    applyTranslations();
    applyTheme();
});

// تهيئة التطبيق
function initializeApp() {
    // تحديد اللغة المحفوظة
    const savedLanguage = localStorage.getItem('language') || 'ar';
    setLanguage(savedLanguage);

    // تحديد الثيم المحفوظ
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);

    // تحميل السلة المحفوظة
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
        cart = JSON.parse(savedCart);
        updateCartUI();
    }
}

// ===== إدارة اللغات =====
function setLanguage(lang) {
    currentLanguage = lang;
    localStorage.setItem('language', lang);
    
    const html = document.documentElement;
    html.setAttribute('lang', lang);
    html.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');
    
    // تحديث أزرار اللغة
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.lang === lang);
    });
    
    applyTranslations();
}

function getCurrentLanguage() {
    return currentLanguage;
}

// ===== إدارة الثيم =====
function setTheme(theme) {
    currentTheme = theme;
    localStorage.setItem('theme', theme);
    
    document.documentElement.setAttribute('data-theme', theme);
    
    // تحديث أيقونة الثيم
    const themeIcon = document.querySelector('#theme-toggle i');
    if (themeIcon) {
        themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
}

function toggleTheme() {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

function applyTheme() {
    setTheme(currentTheme);
}

// ===== تحميل البيانات =====
function loadData() {
    if (typeof products !== 'undefined') {
        loadProducts();
    }
    if (typeof works !== 'undefined') {
        loadWorks();
    }
}

function loadProducts() {
    const productsGrid = document.getElementById('products-grid');
    if (!productsGrid) return;

    const featuredProducts = products.filter(product => product.featured).slice(0, 4);
    
    productsGrid.innerHTML = featuredProducts.map(product => `
        <div class="product-card fade-in">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name[currentLanguage]}" loading="lazy">
                ${product.featured ? `<div class="product-badge">${t('featured')}</div>` : ''}
            </div>
            <div class="product-info">
                <h3 class="product-title">${product.name[currentLanguage]}</h3>
                <p class="product-description">${product.description[currentLanguage]}</p>
                <div class="product-footer">
                    <span class="product-price">$${product.price}</span>
                    <button class="add-to-cart" onclick="addToCart(${product.id})">
                        <i class="fas fa-shopping-cart"></i>
                        ${t('addToCart')}
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

function loadWorks() {
    const worksGrid = document.getElementById('works-grid');
    if (!worksGrid) return;

    worksGrid.innerHTML = works.slice(0, 6).map(work => `
        <div class="work-card fade-in">
            <div class="work-image">
                <img src="${work.image}" alt="${work.title[currentLanguage]}" loading="lazy">
                <div class="work-badge">${work.category[currentLanguage]}</div>
            </div>
            <div class="work-info">
                <h3 class="work-title">${work.title[currentLanguage]}</h3>
                <p class="work-description">${work.description[currentLanguage]}</p>
                <div class="work-footer">
                    <span class="work-year">${work.year}</span>
                    <button class="view-work-btn">
                        <i class="fas fa-eye"></i>
                        ${t('view')}
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// ===== إدارة السلة =====
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const existingItem = cart.find(item => item.id === productId);
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: product.name[currentLanguage],
            price: product.price,
            image: product.image,
            quantity: 1
        });
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartUI();
    showNotification(t('addedToCart'));
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartUI();
}

function updateCartUI() {
    const cartCount = document.getElementById('cart-count');
    if (cartCount) {
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
        cartCount.style.display = totalItems > 0 ? 'block' : 'none';
    }
}

// ===== ربط الأحداث =====
function bindEvents() {
    // أزرار اللغة
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            setLanguage(btn.dataset.lang);
        });
    });

    // زر الثيم
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    // القائمة المحمولة
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });
    }

    // البحث
    const searchInput = document.getElementById('search-input');
    const clearSearch = document.getElementById('clear-search');
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
        
        if (clearSearch) {
            clearSearch.addEventListener('click', () => {
                searchInput.value = '';
                handleSearch();
                toggleClearSearch();
            });
        }
    }

    // تأثيرات التمرير
    addScrollEffects();
}

// ===== البحث =====
function handleSearch() {
    const searchTerm = document.getElementById('search-input')?.value.toLowerCase() || '';
    toggleClearSearch();
    
    if (typeof productsPage !== 'undefined' && productsPage.applyFilters) {
        productsPage.filters.search = searchTerm;
        productsPage.applyFilters();
    }
}

function toggleClearSearch() {
    const searchInput = document.getElementById('search-input');
    const clearSearch = document.getElementById('clear-search');
    
    if (searchInput && clearSearch) {
        const hasValue = searchInput.value.length > 0;
        clearSearch.classList.toggle('hidden', !hasValue);
    }
}

// ===== المساعدات =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showNotification(message) {
    // إنشاء إشعار بسيط
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: var(--primary-600);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        animation: slideInRight 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// ===== تأثيرات التمرير =====
function addScrollEffects() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('fade-in');
                }, index * 100);
            }
        });
    }, observerOptions);

    // مراقبة العناصر
    const elementsToAnimate = document.querySelectorAll('.card, .product-card, .work-card');
    elementsToAnimate.forEach(el => {
        observer.observe(el);
    });
}

// ===== الترجمات =====
function applyTranslations() {
    document.querySelectorAll('[data-key]').forEach(element => {
        const key = element.getAttribute('data-key');
        const translation = t(key);
        if (translation) {
            element.textContent = translation;
        }
    });
    
    // إعادة تحميل البيانات مع الترجمات الجديدة
    loadData();
}

function t(key) {
    if (typeof translations === 'undefined') return key;
    return translations[currentLanguage]?.[key] || key;
}

// ===== تصدير الوظائف العامة =====
window.setLanguage = setLanguage;
window.toggleTheme = toggleTheme;
window.addToCart = addToCart;
window.removeFromCart = removeFromCart;
window.getCurrentLanguage = getCurrentLanguage;
