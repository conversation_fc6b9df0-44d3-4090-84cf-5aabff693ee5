/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Primary Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Success Colors */
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;

    /* Error Colors */
    --error-50: #fef2f2;
    --error-500: #ef4444;
    --error-600: #dc2626;

    /* Warning Colors */
    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;

    /* Typography */
    --font-arabic: 'Cairo', sans-serif;
    --font-latin: 'Inter', sans-serif;

    /* Font Sizes - مقاسات محسنة لجميع الشاشات */
    --font-size-xs: 0.75rem;      /* 12px */
    --font-size-sm: 0.875rem;     /* 14px */
    --font-size-base: 1rem;       /* 16px */
    --font-size-lg: 1.125rem;     /* 18px */
    --font-size-xl: 1.25rem;      /* 20px */
    --font-size-2xl: 1.5rem;      /* 24px */
    --font-size-3xl: 1.875rem;    /* 30px */
    --font-size-4xl: 2.25rem;     /* 36px */
    --font-size-5xl: 3rem;        /* 48px */
    --font-size-6xl: 3.75rem;     /* 60px */
    --font-size-7xl: 4.5rem;      /* 72px */
    --font-size-8xl: 6rem;        /* 96px */

    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 80px 0;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --border-radius-2xl: 24px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Light Mode Colors */
body.light-mode {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-accent: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-tertiary: #94a3b8;
    --text-inverse: #ffffff;
    --border-color: #e2e8f0;
    --border-color-light: #f1f5f9;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --overlay-color: rgba(0, 0, 0, 0.5);
}

/* Dark Mode Colors */
body.dark-mode {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-accent: #475569;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --text-inverse: #1e293b;
    --border-color: #475569;
    --border-color-light: #334155;
    --shadow-color: rgba(0, 0, 0, 0.4);
    --overlay-color: rgba(0, 0, 0, 0.7);
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="ltr"] {
    text-align: left;
}

/* Base Typography */
body {
    font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.7;
    font-weight: 400;
    font-size: 16px;
    transition: var(--transition);
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: -0.01em;
}

/* Arabic Typography */
[lang="ar"] body,
[dir="rtl"] body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
    letter-spacing: 0;
}

/* English Typography */
[lang="en"] body,
[dir="ltr"] body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    letter-spacing: -0.01em;
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition);
}

.dark-mode .header {
    background: rgba(15, 23, 42, 0.95);
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    min-height: 70px;
}

.logo,
.logo-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition-fast);
}

.logo:hover,
.logo-link:hover {
    transform: translateY(-1px);
}

.logo-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.3rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
    opacity: 0;
}

.logo:hover .logo-icon::before,
.logo-link:hover .logo-icon::before {
    opacity: 1;
    animation: shine 0.6s ease-in-out;
}

@keyframes shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

.logo:hover .logo-icon,
.logo-link:hover .logo-icon {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.logo-text {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--text-primary);
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, var(--text-primary), var(--primary-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95rem;
    transition: var(--transition-fast);
    position: relative;
    padding: 0.5rem 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-600);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    transition: var(--transition-fast);
    border-radius: 1px;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-btn,
.theme-toggle,
.lang-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.75rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.cart-btn:hover,
.theme-toggle:hover,
.lang-btn:hover {
    background: var(--bg-secondary);
    color: var(--primary-600);
    transform: translateY(-1px);
}

.cart-btn:active,
.theme-toggle:active,
.lang-btn:active {
    transform: translateY(0);
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--primary-500);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.language-selector {
    position: relative;
}

.lang-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 25px var(--shadow-color);
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
}

.lang-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lang-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
}

.lang-option:hover {
    background: var(--bg-secondary);
}

.mobile-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    gap: 4px;
}

.mobile-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
}

.dark-mode .hero {
    background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 50%, var(--primary-900) 100%);
}

.hero-bg {
    position: absolute;
    inset: 0;
    background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');
    opacity: 0.1;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4.5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.hero-subtitle {
    font-size: 1.3rem;
    opacity: 0.95;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    font-weight: 400;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    border: 2px solid transparent;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-width: 140px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-600);
    color: white;
    box-shadow: var(--shadow-md);
    border: none;
}

.btn-primary:hover {
    background: var(--primary-700);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-outline:hover {
    background: var(--bg-secondary);
    color: var(--primary-600);
    border-color: var(--primary-600);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    min-width: auto;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    opacity: 0.8;
    font-size: 0.9rem;
}

.hero-image {
    position: relative;
}

.hero-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.floating-card {
    position: absolute;
    bottom: -20px;
    left: -20px;
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 25px var(--shadow-color);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-100);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    font-size: 1.5rem;
}

.rating {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.rating-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Sections */
section {
    padding: var(--section-padding);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    line-height: 1.2;
    letter-spacing: -0.03em;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
    font-weight: 400;
}

.section-footer {
    text-align: center;
    margin-top: 3rem;
}

/* Services Section */
.services {
    background: var(--bg-secondary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--bg-primary);
    padding: 3rem 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--border-color-light);
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-12px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-300);
}

.service-icon {
    font-size: 4rem;
    margin-bottom: 2rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: var(--primary-600);
    display: block;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
    color: var(--primary-700);
}

.service-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.25rem;
    color: var(--text-primary);
    line-height: 1.4;
    letter-spacing: -0.02em;
}

.service-description {
    color: var(--text-secondary);
    line-height: 1.7;
    font-size: 1rem;
    font-weight: 400;
    margin-bottom: 0;
    flex-grow: 1;
}

/* Products Section */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
    padding: 1rem 0;
}

.product-card {
    background: var(--bg-primary);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid var(--border-color-light);
    height: 100%;
    display: flex;
    flex-direction: column;
    transform: translateY(0);
}

.product-card:hover {
    transform: translateY(-16px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    border-color: var(--primary-400);
}

.product-image {
    position: relative;
    height: 240px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.08);
}

.product-badge {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    padding: 0.25rem 0.625rem;
    border-radius: 16px;
    font-size: 0.7rem;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.product-info {
    padding: 1.25rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.5rem;
}



.product-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    line-height: 1.3;
    letter-spacing: 0;
}

.product-description {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
    flex-grow: 1;
}

.product-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.5rem;
}

.product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-600);
    letter-spacing: 0;
}

.add-to-cart {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    letter-spacing: 0;
    white-space: nowrap;
}

.add-to-cart:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.add-to-cart:active {
    transform: translateY(0);
}

/* About Section */
.about {
    background: var(--bg-secondary);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-features {
    margin-top: 2rem;
}

.feature {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-100);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.feature-content p {
    color: var(--text-secondary);
    line-height: 1.5;
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

/* Works Section */
.works {
    background: var(--bg-primary);
}

.works-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
    padding: 1rem 0;
}

.work-card {
    background: var(--bg-primary);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    display: flex;
    flex-direction: column;
    transform: translateY(0);
}

.work-card:hover {
    transform: translateY(-16px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.work-image {
    position: relative;
    height: 240px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.work-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.work-card:hover .work-image img {
    transform: scale(1.08);
}



.work-badge {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    padding: 0.25rem 0.625rem;
    border-radius: 16px;
    font-size: 0.7rem;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}



.work-info {
    padding: 1.25rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.5rem;
}

.work-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    line-height: 1.3;
    letter-spacing: 0;
}

.work-description {
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: 0.75rem;
    font-size: 0.8rem;
    flex-grow: 1;
}

.work-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.5rem;
}

.work-year {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-600);
    letter-spacing: 0;
}

.view-work-btn {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    letter-spacing: 0;
    white-space: nowrap;
}

.view-work-btn:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.view-work-btn:active {
    transform: translateY(0);
}

/* Contact Section */
.contact {
    background: var(--bg-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-100);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    font-size: 1.25rem;
}

.contact-details h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.contact-details p {
    color: var(--text-secondary);
}

.contact-form {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: var(--transition);
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-500);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--bg-tertiary);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.footer-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--bg-primary);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}

.footer-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a,
.footer-links span {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-600);
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.copyright,
.made-with {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.made-with i {
    color: #ef4444;
    margin: 0 0.25rem;
}

/* Modal */
.modal {
    position: fixed;
    inset: 0;
    background: var(--overlay-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: var(--transition);
    box-shadow: var(--shadow-2xl);
    border: 1px solid var(--border-color);
}

.modal.active .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

/* Cart */
.cart-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.cart-item-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.cart-item-price {
    color: var(--primary-600);
    font-weight: 600;
}

.cart-item-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.quantity-btn:hover {
    background: var(--bg-secondary);
}

.remove-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.remove-btn:hover {
    background: #dc2626;
}

.cart-empty {
    text-align: center;
    padding: 2rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.cart-total {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.total-amount {
    color: var(--primary-600);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--text-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}
