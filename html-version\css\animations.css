/* ملف الرسوم المتحركة والتأثيرات البصرية */

/* رسوم متحركة للتحميل */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--primary-500);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-500), 0 0 30px var(--primary-500);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* فئات الرسوم المتحركة */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

.animate-slide-left {
    animation: slideInFromLeft 0.6s ease-out forwards;
}

.animate-slide-right {
    animation: slideInFromRight 0.6s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-rotate {
    animation: rotate 1s linear infinite;
}

.animate-slide-down {
    animation: slideDown 0.4s ease-out forwards;
}

.animate-slide-up {
    animation: slideUp 0.4s ease-out forwards;
}

/* تأثيرات التمرير */
.on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.on-scroll.animate {
    opacity: 1;
    transform: translateY(0);
}

/* تأثيرات الهوفر المحسنة */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px var(--primary-500);
}

/* تأثيرات النقر */
.click-effect {
    position: relative;
    overflow: hidden;
}

.click-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.click-effect:active::before {
    width: 200px;
    height: 200px;
}

/* تأثيرات التحميل */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* تأثيرات الكتابة */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--primary-500);
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--primary-500);
    }
}

/* تأثيرات الخلفية */
.bg-animate {
    background: linear-gradient(-45deg, var(--primary-500), var(--primary-600), var(--primary-700), var(--primary-800));
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* تأثيرات الجسيمات */
.particles {
    position: relative;
    overflow: hidden;
}

.particles::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
                      radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
                      radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: particles 20s linear infinite;
    pointer-events: none;
}

@keyframes particles {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-100px);
    }
}

/* تأثيرات الموجات */
.wave-effect {
    position: relative;
    overflow: hidden;
}

.wave-effect::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: wave 3s ease-in-out infinite;
}

@keyframes wave {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* تحسينات الأداء */
.gpu-layer {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: transform;
}

/* تأثيرات الانتقال المحسنة */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fast-transition {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.slow-transition {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات خاصة للوضع الليلي */
.dark-mode .animate-glow {
    animation: glow-dark 2s ease-in-out infinite;
}

@keyframes glow-dark {
    0%, 100% {
        box-shadow: 0 0 5px var(--primary-400);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-400), 0 0 30px var(--primary-400);
    }
}

/* تأثيرات الظهور التدريجي */
.stagger-animation > * {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
