/* Responsive Design */

/* Extra Large Screens (1400px and up) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }

    .hero-content {
        gap: 6rem;
    }

    .services-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .products-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .works-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Large Screens (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
    }

    .hero-content {
        gap: 5rem;
    }

    .services-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .works-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Medium Screens (768px to 1199px) */
@media (max-width: 1199px) {
    .container {
        padding: 0 15px;
    }

    .hero-content {
        gap: 3rem;
    }

    .about-content,
    .contact-content {
        gap: 3rem;
    }
}

/* Tablet Screens (768px to 991px) */
@media (max-width: 991px) {
    :root {
        --section-padding: 60px 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-image {
        order: -1;
    }

    .hero-stats {
        justify-content: center;
        max-width: 400px;
        margin: 0 auto;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .about-image {
        order: -1;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .works-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile Screens (768px and below) */
@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        border-top: 1px solid var(--border-color);
        flex-direction: column;
        padding: 2rem;
        gap: 1rem;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
        z-index: 1000;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .mobile-toggle {
        display: flex;
    }

    .mobile-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .mobile-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .nav-controls {
        gap: 0.5rem;
    }

    .lang-menu {
        right: auto;
        left: 0;
    }

    /* Hero Section */
    .hero {
        min-height: 80vh;
        padding-top: 80px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: 200px;
    }

    .floating-card {
        position: static;
        margin-top: 1rem;
        justify-content: center;
    }

    /* Sections */
    :root {
        --section-padding: 40px 0;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    /* Grids */
    .services-grid,
    .products-grid,
    .works-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    /* Cards */
    .service-card,
    .product-card,
    .work-card {
        margin: 0 auto;
        max-width: 400px;
    }

    /* Contact Form */
    .form-row {
        grid-template-columns: 1fr;
    }

    .contact-form {
        padding: 1.5rem;
    }

    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    /* Modal */
    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }

    .modal-footer {
        flex-direction: column;
        gap: 1rem;
    }

    .cart-total {
        text-align: center;
    }

    /* Cart Items */
    .cart-item {
        flex-direction: column;
        text-align: center;
    }

    .cart-item-image {
        align-self: center;
    }

    .cart-item-actions {
        justify-content: center;
    }
}

/* Small Mobile Screens (480px and below) */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    /* Header */
    .navbar {
        padding: 0.75rem 0;
    }

    .logo-text {
        font-size: 1.25rem;
    }

    .logo-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    /* Hero */
    .hero-title {
        font-size: 2rem;
        line-height: 1.3;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-image img {
        height: 300px;
    }

    /* Buttons */
    .btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }

    /* Cards */
    .service-card,
    .product-card,
    .work-card {
        padding: 1.5rem;
    }

    .service-icon {
        font-size: 2.5rem;
    }

    .product-image,
    .work-image {
        height: 180px;
    }

    /* Features */
    .feature {
        flex-direction: column;
        text-align: center;
    }

    .feature-icon {
        align-self: center;
        margin-bottom: 1rem;
    }

    /* Contact */
    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .contact-icon {
        align-self: center;
    }

    /* Form */
    .form-group input,
    .form-group textarea {
        padding: 0.625rem 0.875rem;
        font-size: 0.9rem;
    }

    /* Footer */
    .footer {
        padding: 2rem 0 1rem;
    }

    .footer-content {
        gap: 1.5rem;
    }

    .social-link {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

/* Extra Small Screens (360px and below) */
@media (max-width: 360px) {
    .hero-title {
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
    }

    .service-card,
    .product-card,
    .work-card {
        padding: 1.25rem;
    }

    .modal-content {
        width: 98%;
        margin: 0.5rem;
    }
}

/* Landscape Mobile Orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
    }

    .hero-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .hero-image {
        order: 0;
    }

    .hero-stats {
        grid-template-columns: repeat(3, 1fr);
        max-width: none;
    }
}

/* Print Styles */
@media print {
    .header,
    .mobile-toggle,
    .nav-controls,
    .floating-card,
    .modal {
        display: none !important;
    }

    .hero {
        min-height: 60vh;
        padding: 2rem 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    section {
        padding: 1rem 0;
        break-inside: avoid;
    }

    .service-card,
    .product-card,
    .work-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .btn {
        display: none;
    }

    .footer {
        padding: 1rem 0;
    }

    .social-links {
        display: none;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-bg {
        background-size: 30px 30px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .hero-image img,
    .product-image img,
    .work-image img {
        transform: none !important;
    }

    .service-card:hover,
    .product-card:hover,
    .work-card:hover {
        transform: none !important;
    }
}

/* Dark Mode Specific Responsive Adjustments */
.dark-mode .nav-menu {
    background: var(--bg-primary);
    border-top-color: var(--border-color);
}

.dark-mode .floating-card {
    background: var(--bg-primary);
    box-shadow: 0 10px 25px var(--shadow-color);
}

/* Focus Styles for Accessibility */
@media (max-width: 768px) {
    .nav-link:focus,
    .btn:focus,
    .cart-btn:focus,
    .theme-toggle:focus,
    .lang-btn:focus {
        outline: 2px solid var(--primary-500);
        outline-offset: 2px;
    }

    .mobile-toggle:focus {
        outline: 2px solid var(--primary-500);
        outline-offset: 2px;
        border-radius: 4px;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .service-card:hover,
    .product-card:hover,
    .work-card:hover {
        transform: none;
    }

    .nav-link:hover::after {
        width: 0;
    }

    .nav-link.active::after {
        width: 100%;
    }

    /* Increase touch targets */
    .cart-btn,
    .theme-toggle,
    .lang-btn,
    .mobile-toggle {
        min-width: 44px;
        min-height: 44px;
    }

    .add-to-cart,
    .quantity-btn,
    .remove-btn {
        min-width: 44px;
        min-height: 44px;
    }
}

/* Tablet Specific Adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
    .hero-content {
        gap: 3rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .products-grid,
    .works-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .about-content,
    .contact-content {
        gap: 3rem;
    }
}

/* Large Mobile Screens (414px to 767px) */
@media (min-width: 414px) and (max-width: 767px) {
    .hero-title {
        font-size: 2.25rem;
    }

    .section-title {
        font-size: 2.25rem;
    }

    .service-card,
    .product-card,
    .work-card {
        max-width: 450px;
    }

    .hero-image img {
        height: 350px;
    }

    .product-image,
    .work-image {
        height: 200px;
    }
}
