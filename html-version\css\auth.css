/* صفحات المصادقة - تسجيل الدخول ولوحة التحكم */

/* Auth Page Layout */
.auth-page {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    min-height: 100vh;
}

.dark-mode.auth-page {
    background: linear-gradient(135deg, var(--primary-950) 0%, var(--primary-900) 100%);
}

.auth-main {
    padding-top: 80px;
    min-height: calc(100vh - 80px);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.auth-container {
    width: 100%;
    max-width: 450px;
    padding: 2rem;
    position: relative;
    z-index: 10;
    pointer-events: auto;
}

/* Auth Card */
.auth-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-xl);
    padding: 3rem;
    box-shadow: var(--shadow-2xl);
    border: 1px solid var(--border-color-light);
    position: relative;
    overflow: hidden;
    z-index: 20;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600), var(--primary-700));
}

/* Auth Header */
.auth-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.auth-logo .logo-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
    box-shadow: var(--shadow-lg);
}

.auth-logo .logo-text {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.5;
}

/* Auth Form */
.auth-form {
    margin-bottom: 2rem;
    position: relative;
    z-index: 30;
    pointer-events: auto;
}

.auth-form * {
    pointer-events: auto;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
    z-index: 998;
    pointer-events: auto;
    isolation: isolate;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    z-index: 5;
    pointer-events: none;
}

[dir="rtl"] .input-icon {
    left: auto;
    right: 1rem;
}

.form-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    font-size: 1rem;
    transition: var(--transition);
    position: relative;
    z-index: 999;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    box-sizing: border-box;
    font-family: inherit;
}

[dir="rtl"] .form-input {
    padding: 1rem 3rem 1rem 1rem;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:focus + .input-icon {
    color: var(--primary-500);
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition-fast);
    z-index: 50;
}

[dir="rtl"] .password-toggle {
    right: auto;
    left: 1rem;
}

.password-toggle:hover {
    background: var(--bg-secondary);
    color: var(--primary-500);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    -webkit-user-select: none;
    user-select: none;
}

.checkbox-wrapper input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition-fast);
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-500);
    border-color: var(--primary-500);
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
}

.checkbox-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.forgot-password {
    color: var(--primary-600);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition-fast);
}

.forgot-password:hover {
    color: var(--primary-700);
    text-decoration: underline;
}

/* Auth Submit Button */
.auth-submit {
    width: 100%;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.auth-submit:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Demo Credentials */
.demo-credentials {
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    border: 2px solid var(--primary-200);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
}

.dark-mode .demo-credentials {
    background: linear-gradient(135deg, var(--primary-900), var(--primary-800));
    border-color: var(--primary-700);
}

.demo-credentials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600), var(--primary-700));
}

.demo-credentials:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-400);
}

.demo-credentials h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    color: var(--primary-700);
    font-weight: 700;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.dark-mode .demo-credentials h4 {
    color: var(--primary-300);
}

.demo-credentials h4::before {
    content: '🔑';
    font-size: 1.2rem;
}

.demo-item {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.75rem;
    border: 1px solid var(--border-color-light);
    transition: var(--transition-fast);
    text-align: center;
}

.demo-item:hover {
    background: var(--primary-50);
    border-color: var(--primary-300);
}

.dark-mode .demo-item:hover {
    background: var(--primary-900);
}

.demo-item:last-child {
    margin-bottom: 0;
}

.demo-item strong {
    color: var(--primary-600);
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-block;
    margin-left: 0.5rem;
}

.dark-mode .demo-item strong {
    color: var(--primary-400);
}

[dir="rtl"] .demo-item strong {
    margin-left: 0;
    margin-right: 0.5rem;
}

.demo-item span {
    color: var(--text-primary);
    font-weight: 700;
    font-family: 'Courier New', monospace;
    background: var(--bg-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    margin: 0 0.25rem;
}

.demo-note {
    margin: 1rem 0 0 0;
    font-size: 0.8rem;
    color: var(--text-tertiary);
    text-align: center;
    font-style: italic;
}

/* Auth Footer */
.auth-footer {
    text-align: center;
}

.auth-footer-text {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.auth-link {
    color: var(--primary-600);
    text-decoration: none;
    font-weight: 500;
    margin-right: 0.5rem;
    transition: var(--transition-fast);
}

[dir="rtl"] .auth-link {
    margin-right: 0;
    margin-left: 0.5rem;
}

.auth-link:hover {
    color: var(--primary-700);
    text-decoration: underline;
}

.auth-divider {
    position: relative;
    margin: 1.5rem 0;
    text-align: center;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.auth-divider span {
    background: var(--bg-primary);
    padding: 0 1rem;
    color: var(--text-tertiary);
    font-size: 0.9rem;
    position: relative;
    z-index: 1;
}

/* Background Elements */
.auth-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 1;
    pointer-events: none;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-200), var(--primary-300));
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
    pointer-events: none;
    z-index: 0;
}

.dark-mode .bg-shape {
    background: linear-gradient(135deg, var(--primary-800), var(--primary-700));
    opacity: 0.05;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-content p {
    margin-top: 1rem;
    font-size: 1rem;
}

/* Notification */
.notification {
    position: fixed;
    top: 100px;
    right: 2rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-lg);
    z-index: 9998;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    min-width: 300px;
}

[dir="rtl"] .notification {
    right: auto;
    left: 2rem;
    transform: translateX(-100%);
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-icon {
    font-size: 1.2rem;
}

.notification.success {
    border-left: 4px solid var(--success-500);
}

.notification.success .notification-icon {
    color: var(--success-500);
}

.notification.error {
    border-left: 4px solid var(--error-500);
}

.notification.error .notification-icon {
    color: var(--error-500);
}

[dir="rtl"] .notification.success,
[dir="rtl"] .notification.error {
    border-left: none;
    border-right: 4px solid;
}

.notification-message {
    color: var(--text-primary);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        padding: 1rem;
    }

    .auth-card {
        padding: 2rem 1.5rem;
    }

    .auth-title {
        font-size: 1.5rem;
    }

    .form-options {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .notification {
        right: 1rem;
        left: 1rem;
        min-width: auto;
        transform: translateY(-100%);
    }

    .notification.show {
        transform: translateY(0);
    }

    [dir="rtl"] .notification {
        transform: translateY(-100%);
    }

    [dir="rtl"] .notification.show {
        transform: translateY(0);
    }
}
