// صفحة المنتجات - وظائف متقدمة
class ProductsPage {
    constructor() {
        this.allProducts = [...products];
        this.filteredProducts = [...products];
        this.displayedProducts = [];
        this.currentPage = 1;
        this.productsPerPage = 12;
        this.viewMode = 'grid';
        this.filters = {
            search: ''
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateStats();
        this.applyFilters();
        this.renderProducts();
    }

    setupEventListeners() {
        // البحث
        const searchInput = document.getElementById('search-input');
        const clearSearch = document.getElementById('clear-search');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.toggleClearSearch();
                this.debounceFilter();
            });
        }

        if (clearSearch) {
            clearSearch.addEventListener('click', () => {
                searchInput.value = '';
                this.filters.search = '';
                this.toggleClearSearch();
                this.applyFilters();
            });
        }

        // إعادة تعيين الفلاتر
        const resetFilters = document.getElementById('reset-filters');

        if (resetFilters) {
            resetFilters.addEventListener('click', () => {
                this.resetAllFilters();
            });
        }

        // تحميل المزيد
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreProducts();
            });
        }
    }



    updateStats() {
        const totalProducts = document.getElementById('total-products');

        if (totalProducts) {
            totalProducts.textContent = this.allProducts.length;
        }
    }

    toggleClearSearch() {
        const clearSearch = document.getElementById('clear-search');
        if (clearSearch) {
            if (this.filters.search) {
                clearSearch.classList.remove('hidden');
            } else {
                clearSearch.classList.add('hidden');
            }
        }
    }

    debounceFilter() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.applyFilters();
        }, 300);
    }

    applyFilters() {
        this.showLoading();

        setTimeout(() => {
            let filtered = [...this.allProducts];
            const currentLang = getCurrentLanguage();

            // تطبيق فلتر البحث
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                filtered = filtered.filter(product =>
                    product.name[currentLang].toLowerCase().includes(searchTerm) ||
                    product.description[currentLang].toLowerCase().includes(searchTerm)
                );
            }

            // ترتيب حسب الاسم
            filtered.sort((a, b) => {
                return a.name[currentLang].localeCompare(b.name[currentLang]);
            });

            this.filteredProducts = filtered;
            this.currentPage = 1;
            this.updateActiveFilters();
            this.updateResultsInfo();
            this.renderProducts();
            this.hideLoading();
        }, 500);
    }

    updateActiveFilters() {
        // لا حاجة لعرض الفلاتر النشطة بعد الآن
    }



    resetAllFilters() {
        this.filters = {
            search: ''
        };

        // إعادة تعيين عناصر الواجهة
        const searchInput = document.getElementById('search-input');

        if (searchInput) searchInput.value = '';

        this.toggleClearSearch();
        this.applyFilters();
    }

    updateResultsInfo() {
        const showingCount = document.getElementById('showing-count');
        const totalCount = document.getElementById('total-count');

        if (showingCount && totalCount) {
            const showing = Math.min(this.currentPage * this.productsPerPage, this.filteredProducts.length);
            showingCount.textContent = showing;
            totalCount.textContent = this.filteredProducts.length;
        }
    }



    renderProducts() {
        const productsGrid = document.getElementById('products-grid');
        const noResults = document.getElementById('no-results');
        const loadMoreContainer = document.getElementById('load-more-container');

        if (!productsGrid) return;

        if (this.filteredProducts.length === 0) {
            productsGrid.innerHTML = '';
            if (noResults) noResults.classList.remove('hidden');
            if (loadMoreContainer) loadMoreContainer.classList.add('hidden');
            return;
        }

        if (noResults) noResults.classList.add('hidden');

        // حساب المنتجات المعروضة
        const endIndex = this.currentPage * this.productsPerPage;
        this.displayedProducts = this.filteredProducts.slice(0, endIndex);

        const currentLang = getCurrentLanguage();
        productsGrid.innerHTML = this.displayedProducts.map(product => `
            <div class="product-card animate-fade-in">
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name[currentLang]}" loading="lazy">
                    ${product.featured ? `<div class="product-badge">${currentLang === 'ar' ? 'مميز' : currentLang === 'en' ? 'Featured' : 'Öne Çıkan'}</div>` : ''}
                </div>
                <div class="product-info">
                    <h3 class="product-title">${product.name[currentLang]}</h3>
                    <p class="product-description">${product.description[currentLang]}</p>
                    <div class="product-footer">
                        <span class="product-price">$${product.price}</span>
                        <button type="button" class="add-to-cart" data-product-id="${product.id}">
                            <i class="fas fa-shopping-cart"></i>
                            ${t('addToCart')}
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        // إظهار/إخفاء زر تحميل المزيد
        if (loadMoreContainer) {
            if (this.displayedProducts.length < this.filteredProducts.length) {
                loadMoreContainer.classList.remove('hidden');
            } else {
                loadMoreContainer.classList.add('hidden');
            }
        }

        this.updateResultsInfo();
    }

    loadMoreProducts() {
        this.currentPage++;
        this.renderProducts();
    }

    showLoading() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('hidden');
        }
    }

    hideLoading() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.classList.add('hidden');
        }
    }
}

// تهيئة صفحة المنتجات عند تحميل الصفحة
let productsPage;

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من أننا في صفحة المنتجات
    if (document.getElementById('products-grid')) {
        productsPage = new ProductsPage();
    }
});
