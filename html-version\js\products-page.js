// صفحة المنتجات - وظائف متقدمة
class ProductsPage {
    constructor() {
        this.allProducts = [...products];
        this.filteredProducts = [...products];
        this.displayedProducts = [];
        this.currentPage = 1;
        this.productsPerPage = 12;
        this.viewMode = 'grid';
        this.filters = {
            search: '',
            category: 'all',
            priceRange: 'all',
            sortBy: 'name'
        };
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.populateFilters();
        this.updateStats();
        this.applyFilters();
        this.renderProducts();
    }

    setupEventListeners() {
        // البحث
        const searchInput = document.getElementById('search-input');
        const clearSearch = document.getElementById('clear-search');
        
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.toggleClearSearch();
                this.debounceFilter();
            });
        }

        if (clearSearch) {
            clearSearch.addEventListener('click', () => {
                searchInput.value = '';
                this.filters.search = '';
                this.toggleClearSearch();
                this.applyFilters();
            });
        }

        // الفلاتر
        const categoryFilter = document.getElementById('category-filter');
        const priceFilter = document.getElementById('price-filter');
        const sortFilter = document.getElementById('sort-filter');

        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filters.category = e.target.value;
                this.applyFilters();
            });
        }

        if (priceFilter) {
            priceFilter.addEventListener('change', (e) => {
                this.filters.priceRange = e.target.value;
                this.applyFilters();
            });
        }

        if (sortFilter) {
            sortFilter.addEventListener('change', (e) => {
                this.filters.sortBy = e.target.value;
                this.applyFilters();
            });
        }

        // عرض الشبكة/القائمة
        const gridView = document.getElementById('grid-view');
        const listView = document.getElementById('list-view');

        if (gridView) {
            gridView.addEventListener('click', () => {
                this.setViewMode('grid');
            });
        }

        if (listView) {
            listView.addEventListener('click', () => {
                this.setViewMode('list');
            });
        }

        // إعادة تعيين الفلاتر
        const resetFilters = document.getElementById('reset-filters');
        const clearAllFilters = document.getElementById('clear-all-filters');

        if (resetFilters) {
            resetFilters.addEventListener('click', () => {
                this.resetAllFilters();
            });
        }

        if (clearAllFilters) {
            clearAllFilters.addEventListener('click', () => {
                this.resetAllFilters();
            });
        }

        // تحميل المزيد
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreProducts();
            });
        }
    }

    populateFilters() {
        const categoryFilter = document.getElementById('category-filter');
        if (!categoryFilter) return;

        // استخراج الفئات الفريدة
        const categories = [...new Set(this.allProducts.map(product => 
            product.category[getCurrentLanguage()]
        ))];

        // إضافة الفئات إلى القائمة
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categoryFilter.appendChild(option);
        });
    }

    updateStats() {
        const totalProducts = document.getElementById('total-products');
        const totalCategories = document.getElementById('total-categories');

        if (totalProducts) {
            totalProducts.textContent = this.allProducts.length;
        }

        if (totalCategories) {
            const categories = [...new Set(this.allProducts.map(product => 
                product.category[getCurrentLanguage()]
            ))];
            totalCategories.textContent = categories.length;
        }
    }

    toggleClearSearch() {
        const clearSearch = document.getElementById('clear-search');
        if (clearSearch) {
            if (this.filters.search) {
                clearSearch.classList.remove('hidden');
            } else {
                clearSearch.classList.add('hidden');
            }
        }
    }

    debounceFilter() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.applyFilters();
        }, 300);
    }

    applyFilters() {
        this.showLoading();
        
        setTimeout(() => {
            let filtered = [...this.allProducts];
            const currentLang = getCurrentLanguage();

            // تطبيق فلتر البحث
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                filtered = filtered.filter(product => 
                    product.name[currentLang].toLowerCase().includes(searchTerm) ||
                    product.description[currentLang].toLowerCase().includes(searchTerm) ||
                    product.category[currentLang].toLowerCase().includes(searchTerm)
                );
            }

            // تطبيق فلتر الفئة
            if (this.filters.category !== 'all') {
                filtered = filtered.filter(product => 
                    product.category[currentLang] === this.filters.category
                );
            }

            // تطبيق فلتر السعر
            if (this.filters.priceRange !== 'all') {
                filtered = filtered.filter(product => {
                    const price = product.price;
                    switch (this.filters.priceRange) {
                        case '0-100':
                            return price >= 0 && price <= 100;
                        case '100-200':
                            return price > 100 && price <= 200;
                        case '200-300':
                            return price > 200 && price <= 300;
                        case '300+':
                            return price > 300;
                        default:
                            return true;
                    }
                });
            }

            // تطبيق الترتيب
            filtered.sort((a, b) => {
                switch (this.filters.sortBy) {
                    case 'name':
                        return a.name[currentLang].localeCompare(b.name[currentLang]);
                    case 'price-low':
                        return a.price - b.price;
                    case 'price-high':
                        return b.price - a.price;
                    case 'featured':
                        return (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
                    default:
                        return 0;
                }
            });

            this.filteredProducts = filtered;
            this.currentPage = 1;
            this.updateActiveFilters();
            this.updateResultsInfo();
            this.renderProducts();
            this.hideLoading();
        }, 500);
    }

    updateActiveFilters() {
        const filtersList = document.getElementById('filters-list');
        const clearAllBtn = document.getElementById('clear-all-filters');
        const activeFiltersSection = document.getElementById('active-filters');
        
        if (!filtersList) return;

        filtersList.innerHTML = '';
        let hasActiveFilters = false;

        // فلتر البحث
        if (this.filters.search) {
            this.addFilterTag(filtersList, 'search', `البحث: ${this.filters.search}`);
            hasActiveFilters = true;
        }

        // فلتر الفئة
        if (this.filters.category !== 'all') {
            this.addFilterTag(filtersList, 'category', `الفئة: ${this.filters.category}`);
            hasActiveFilters = true;
        }

        // فلتر السعر
        if (this.filters.priceRange !== 'all') {
            this.addFilterTag(filtersList, 'priceRange', `السعر: ${this.filters.priceRange}`);
            hasActiveFilters = true;
        }

        // إظهار/إخفاء زر مسح الكل
        if (clearAllBtn) {
            if (hasActiveFilters) {
                clearAllBtn.classList.remove('hidden');
            } else {
                clearAllBtn.classList.add('hidden');
            }
        }

        // إظهار/إخفاء قسم الفلاتر النشطة
        if (activeFiltersSection) {
            if (hasActiveFilters) {
                activeFiltersSection.classList.remove('hidden');
            } else {
                activeFiltersSection.classList.add('hidden');
            }
        }
    }

    addFilterTag(container, type, text) {
        const tag = document.createElement('div');
        tag.className = 'filter-tag';
        tag.innerHTML = `
            <span>${text}</span>
            <button type="button" onclick="productsPage.removeFilter('${type}')">
                <i class="fas fa-times"></i>
            </button>
        `;
        container.appendChild(tag);
    }

    removeFilter(type) {
        switch (type) {
            case 'search':
                this.filters.search = '';
                document.getElementById('search-input').value = '';
                this.toggleClearSearch();
                break;
            case 'category':
                this.filters.category = 'all';
                document.getElementById('category-filter').value = 'all';
                break;
            case 'priceRange':
                this.filters.priceRange = 'all';
                document.getElementById('price-filter').value = 'all';
                break;
        }
        this.applyFilters();
    }

    resetAllFilters() {
        this.filters = {
            search: '',
            category: 'all',
            priceRange: 'all',
            sortBy: 'name'
        };

        // إعادة تعيين عناصر الواجهة
        const searchInput = document.getElementById('search-input');
        const categoryFilter = document.getElementById('category-filter');
        const priceFilter = document.getElementById('price-filter');
        const sortFilter = document.getElementById('sort-filter');

        if (searchInput) searchInput.value = '';
        if (categoryFilter) categoryFilter.value = 'all';
        if (priceFilter) priceFilter.value = 'all';
        if (sortFilter) sortFilter.value = 'name';

        this.toggleClearSearch();
        this.applyFilters();
    }

    updateResultsInfo() {
        const showingCount = document.getElementById('showing-count');
        const totalCount = document.getElementById('total-count');

        if (showingCount && totalCount) {
            const showing = Math.min(this.currentPage * this.productsPerPage, this.filteredProducts.length);
            showingCount.textContent = showing;
            totalCount.textContent = this.filteredProducts.length;
        }
    }

    setViewMode(mode) {
        this.viewMode = mode;
        
        const gridBtn = document.getElementById('grid-view');
        const listBtn = document.getElementById('list-view');
        const productsGrid = document.getElementById('products-grid');

        if (gridBtn && listBtn && productsGrid) {
            gridBtn.classList.toggle('active', mode === 'grid');
            listBtn.classList.toggle('active', mode === 'list');
            
            productsGrid.classList.toggle('list-view', mode === 'list');
        }
    }

    renderProducts() {
        const productsGrid = document.getElementById('products-grid');
        const noResults = document.getElementById('no-results');
        const loadMoreContainer = document.getElementById('load-more-container');

        if (!productsGrid) return;

        if (this.filteredProducts.length === 0) {
            productsGrid.innerHTML = '';
            if (noResults) noResults.classList.remove('hidden');
            if (loadMoreContainer) loadMoreContainer.classList.add('hidden');
            return;
        }

        if (noResults) noResults.classList.add('hidden');

        // حساب المنتجات المعروضة
        const endIndex = this.currentPage * this.productsPerPage;
        this.displayedProducts = this.filteredProducts.slice(0, endIndex);

        const currentLang = getCurrentLanguage();
        productsGrid.innerHTML = this.displayedProducts.map(product => `
            <div class="product-card animate-fade-in">
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name[currentLang]}" loading="lazy">
                    ${product.featured ? `<div class="product-badge">${currentLang === 'ar' ? 'مميز' : currentLang === 'en' ? 'Featured' : 'Öne Çıkan'}</div>` : ''}
                </div>
                <div class="product-info">
                    <div class="product-category">${product.category[currentLang]}</div>
                    <h3 class="product-title">${product.name[currentLang]}</h3>
                    <p class="product-description">${product.description[currentLang]}</p>
                    <div class="product-footer">
                        <span class="product-price">$${product.price}</span>
                        <button type="button" class="add-to-cart" data-product-id="${product.id}">
                            <i class="fas fa-shopping-cart"></i>
                            ${t('addToCart')}
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        // إظهار/إخفاء زر تحميل المزيد
        if (loadMoreContainer) {
            if (this.displayedProducts.length < this.filteredProducts.length) {
                loadMoreContainer.classList.remove('hidden');
            } else {
                loadMoreContainer.classList.add('hidden');
            }
        }

        this.updateResultsInfo();
    }

    loadMoreProducts() {
        this.currentPage++;
        this.renderProducts();
    }

    showLoading() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('hidden');
        }
    }

    hideLoading() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.classList.add('hidden');
        }
    }
}

// تهيئة صفحة المنتجات عند تحميل الصفحة
let productsPage;

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من أننا في صفحة المنتجات
    if (document.getElementById('products-grid')) {
        productsPage = new ProductsPage();
    }
});
