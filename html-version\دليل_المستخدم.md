# 📖 دليل المستخدم - موقع Tab3h

## 🎯 مقدمة
هذا الدليل سيساعدك على تعديل موقعك بسهولة دون الحاجة لخبرة برمجية متقدمة.

---

## 📁 هيكل الملفات

```
html-version/
├── index.html              # الصفحة الرئيسية
├── products.html           # صفحة المنتجات
├── css/
│   ├── main.css           # ملف التنسيق الرئيسي
│   └── products-page.css  # تنسيق صفحة المنتجات
├── js/
│   ├── data.js            # بيانات المنتجات والأعمال
│   ├── translations.js   # الترجمات
│   ├── app.js            # الوظائف الرئيسية
│   └── products-page.js  # وظائف صفحة المنتجات
└── images/               # مجلد الصور
```

---

## 🛍️ تعديل المنتجات

### 📍 الملف المطلوب: `js/data.js`

### ✅ إضافة منتج جديد:

```javascript
{
    id: 6,                    // رقم فريد للمنتج
    name: {
        ar: 'اسم المنتج بالعربية',
        en: 'Product Name in English',
        tr: 'Türkçe Ürün Adı'
    },
    description: {
        ar: 'وصف المنتج بالعربية',
        en: 'Product description in English',
        tr: 'Türkçe ürün açıklaması'
    },
    price: 299,               // السعر بالدولار
    image: 'رابط_الصورة',      // رابط صورة المنتج
    category: {
        ar: 'الفئة',
        en: 'Category',
        tr: 'Kategori'
    },
    featured: true            // true = مميز، false = عادي
}
```

### ✏️ تعديل منتج موجود:
1. ابحث عن المنتج بالـ `id`
2. غيّر القيم المطلوبة
3. احفظ الملف

### ❌ حذف منتج:
احذف الكتلة الكاملة للمنتج من `{` إلى `}`

---

## 🎨 تعديل الأعمال (Portfolio)

### 📍 الملف المطلوب: `js/data.js`

### ✅ إضافة عمل جديد:

```javascript
{
    id: 7,
    title: {
        ar: 'عنوان العمل',
        en: 'Work Title',
        tr: 'İş Başlığı'
    },
    description: {
        ar: 'وصف العمل',
        en: 'Work Description',
        tr: 'İş Açıklaması'
    },
    image: 'رابط_الصورة',
    category: {
        ar: 'نوع العمل',
        en: 'Work Type',
        tr: 'İş Türü'
    },
    year: 2024,
    client: 'اسم العميل',
    url: 'رابط_العمل'
}
```

---

## 🖼️ تغيير الصور

### 🌐 استخدام روابط خارجية:
```javascript
image: 'https://images.unsplash.com/photo-1234567890'
```

### 📁 استخدام صور محلية:
1. ضع الصورة في مجلد `images/`
2. استخدم المسار:
```javascript
image: 'images/اسم_الصورة.jpg'
```

### 📏 مقاسات الصور المُوصى بها:
- **صور المنتجات**: 400×300 بكسل
- **صور الأعمال**: 600×400 بكسل
- **صورة البطل**: 800×600 بكسل

---

## 🎨 تغيير الألوان

### 📍 الملف المطلوب: `css/main.css`

### 🎯 الألوان الأساسية (السطر 8-17):

```css
:root {
    --primary-500: #3b82f6;    /* اللون الأساسي */
    --primary-600: #2563eb;    /* أغمق قليلاً */
    --primary-700: #1d4ed8;    /* أغمق أكثر */
}
```

### 🌈 ألوان شائعة للاستخدام:

| اللون | الكود |
|-------|-------|
| أزرق | `#3b82f6` |
| أخضر | `#10b981` |
| أحمر | `#ef4444` |
| بنفسجي | `#8b5cf6` |
| برتقالي | `#f59e0b` |
| وردي | `#ec4899` |

### 🔄 كيفية التغيير:
1. افتح `css/main.css`
2. ابحث عن `--primary-500`
3. غيّر القيمة للون المطلوب
4. احفظ الملف

---

## 📝 تعديل النصوص والترجمات

### 📍 الملف المطلوب: `js/translations.js`

### ✏️ تعديل النصوص:

```javascript
ar: {
    heroTitle: 'العنوان الجديد',
    heroSubtitle: 'النص الفرعي الجديد',
    // ... باقي النصوص
}
```

### 🌍 إضافة لغة جديدة:

```javascript
fr: {  // الفرنسية مثلاً
    heroTitle: 'Nouveau Titre',
    heroSubtitle: 'Nouveau Sous-titre',
    // ... ترجمة جميع النصوص
}
```

---

## 📞 تعديل معلومات الاتصال

### 📍 الملفات المطلوبة: `index.html` و `products.html`

### 📱 تغيير رقم الهاتف:
ابحث عن: `+90 555 123 4567`
غيّره إلى رقمك

### 📧 تغيير الإيميل:
ابحث عن: `<EMAIL>`
غيّره إلى إيميلك

### 🌐 تغيير روابط وسائل التواصل:
```html
<a href="https://facebook.com/صفحتك">
<a href="https://twitter.com/حسابك">
<a href="https://instagram.com/حسابك">
```

---

## ⚙️ إعدادات متقدمة

### 🎛️ تغيير عدد المنتجات المعروضة:

**في الصفحة الرئيسية** (`js/app.js` - السطر 75):
```javascript
.slice(0, 4)  // غيّر 4 للعدد المطلوب
```

**في صفحة المنتجات** (`js/products-page.js` - السطر 8):
```javascript
this.productsPerPage = 12;  // غيّر 12 للعدد المطلوب
```

### 🔄 تغيير اللغة الافتراضية:

في `js/app.js` (السطر 29):
```javascript
const savedLanguage = localStorage.getItem('language') || 'ar';
// غيّر 'ar' إلى 'en' للإنجليزية أو 'tr' للتركية
```

---

## 🚀 نشر الموقع

### 📤 رفع الملفات:
1. ارفع جميع الملفات لخادم الويب
2. تأكد من رفع مجلد `css` و `js` و `images`
3. افتح `index.html` في المتصفح

### ✅ فحص الموقع:
- تأكد من ظهور الصور
- اختبر تغيير اللغة
- اختبر إضافة المنتجات للسلة
- تأكد من عمل روابط التواصل

---

## 🔧 حل المشاكل الشائعة

### ❌ الصور لا تظهر:
- تأكد من صحة رابط الصورة
- تأكد من رفع الصور للخادم
- تحقق من أسماء الملفات (حساسة للأحرف)

### ❌ الألوان لا تتغير:
- امسح ذاكرة التخزين المؤقت للمتصفح
- تأكد من حفظ ملف CSS
- تحقق من صحة كود اللون

### ❌ النصوص لا تتغير:
- تأكد من تعديل ملف الترجمات الصحيح
- تحقق من وجود فاصلة بعد كل سطر
- امسح ذاكرة المتصفح

---

## 📞 الدعم الفني

إذا واجهت أي مشكلة:
1. تأكد من اتباع الخطوات بدقة
2. تحقق من عدم وجود أخطاء إملائية
3. اعمل نسخة احتياطية قبل التعديل
4. اختبر التغييرات على جهازك أولاً

---

## ✨ نصائح مهمة

### 💾 النسخ الاحتياطي:
- اعمل نسخة احتياطية قبل أي تعديل
- احفظ الملفات الأصلية في مكان آمن

### 🧪 الاختبار:
- اختبر الموقع على أجهزة مختلفة
- تأكد من عمل الموقع على الهاتف
- اختبر جميع الروابط والأزرار

### 📱 الاستجابة:
- الموقع مُحسّن للهواتف تلقائياً
- لا تحتاج لتعديلات إضافية للاستجابة

---

**🎉 مبروك! أصبح لديك موقع احترافي قابل للتخصيص بسهولة**

---

## 📋 أمثلة عملية للتعديل

### 🛍️ مثال: إضافة منتج جديد

**الخطوة 1:** افتح ملف `js/data.js`

**الخطوة 2:** ابحث عن نهاية قائمة المنتجات واضف:

```javascript
    },  // نهاية المنتج السابق
    {
        id: 6,
        name: {
            ar: 'ساعة ذكية',
            en: 'Smart Watch',
            tr: 'Akıllı Saat'
        },
        description: {
            ar: 'ساعة ذكية متطورة مع مراقبة الصحة',
            en: 'Advanced smartwatch with health monitoring',
            tr: 'Sağlık izleme özellikli gelişmiş akıllı saat'
        },
        price: 199,
        image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=300&fit=crop',
        category: {
            ar: 'إلكترونيات',
            en: 'Electronics',
            tr: 'Elektronik'
        },
        featured: true
    }
];  // نهاية قائمة المنتجات
```

### 🎨 مثال: تغيير اللون الأساسي للأخضر

**الخطوة 1:** افتح ملف `css/main.css`

**الخطوة 2:** ابحث عن السطور التالية وغيّرها:

```css
/* من */
--primary-500: #3b82f6;
--primary-600: #2563eb;
--primary-700: #1d4ed8;

/* إلى */
--primary-500: #10b981;
--primary-600: #059669;
--primary-700: #047857;
```

### 📝 مثال: تغيير العنوان الرئيسي

**الخطوة 1:** افتح ملف `js/translations.js`

**الخطوة 2:** ابحث عن `heroTitle` وغيّره:

```javascript
ar: {
    heroTitle: 'مرحباً بكم في متجرنا الإلكتروني',  // العنوان الجديد
    // ... باقي النصوص
},
en: {
    heroTitle: 'Welcome to Our Online Store',  // العنوان الجديد
    // ... باقي النصوص
}
```

---

## 🔍 دليل استكشاف الأخطاء

### ❗ خطأ: "المنتج لا يظهر"

**السبب المحتمل:** خطأ في تنسيق JSON

**الحل:**
1. تأكد من وجود فاصلة `,` بعد كل منتج
2. تأكد من إغلاق جميع الأقواس `{}`
3. تأكد من وجود `id` فريد لكل منتج

### ❗ خطأ: "الصفحة فارغة"

**السبب المحتمل:** خطأ في JavaScript

**الحل:**
1. افتح أدوات المطور في المتصفح (F12)
2. انظر لتبويب "Console" للأخطاء
3. تأكد من صحة تنسيق الملفات

### ❗ خطأ: "الألوان لا تتغير"

**السبب المحتمل:** ذاكرة التخزين المؤقت

**الحل:**
1. اضغط Ctrl+F5 لإعادة تحميل الصفحة
2. امسح ذاكرة المتصفح
3. تأكد من حفظ ملف CSS

---

## 📊 جدول الألوان الجاهزة

| اسم اللون | الكود الأساسي | الكود الأغمق | الكود الأغمق جداً |
|-----------|---------------|---------------|------------------|
| أزرق | `#3b82f6` | `#2563eb` | `#1d4ed8` |
| أخضر | `#10b981` | `#059669` | `#047857` |
| أحمر | `#ef4444` | `#dc2626` | `#b91c1c` |
| بنفسجي | `#8b5cf6` | `#7c3aed` | `#6d28d9` |
| برتقالي | `#f59e0b` | `#d97706` | `#b45309` |
| وردي | `#ec4899` | `#db2777` | `#be185d` |
| أصفر | `#eab308` | `#ca8a04` | `#a16207` |
| رمادي | `#6b7280` | `#4b5563` | `#374151` |

---

## 🛠️ أدوات مساعدة

### 🎨 مولد الألوان:
- [Coolors.co](https://coolors.co) - لإنشاء لوحات ألوان
- [Adobe Color](https://color.adobe.com) - أداة ألوان احترافية

### 🖼️ مصادر الصور المجانية:
- [Unsplash](https://unsplash.com) - صور عالية الجودة
- [Pexels](https://pexels.com) - صور مجانية للاستخدام التجاري
- [Pixabay](https://pixabay.com) - صور ورسوم مجانية

### 🔧 أدوات التطوير:
- **Visual Studio Code** - محرر نصوص مجاني
- **Chrome DevTools** - أدوات فحص الموقع
- **Firefox Developer Tools** - أدوات تطوير بديلة

---

## 📈 تحسين الأداء

### ⚡ تسريع الموقع:
1. **ضغط الصور:** استخدم أدوات ضغط الصور
2. **تحسين الكود:** احذف المسافات الزائدة
3. **استخدام CDN:** لتحميل أسرع للخطوط والأيقونات

### 📱 تحسين للهواتف:
- الموقع مُحسّن تلقائياً للهواتف
- اختبر على أجهزة مختلفة
- تأكد من سهولة النقر على الأزرار

### 🔍 تحسين محركات البحث (SEO):
1. **العناوين:** استخدم عناوين وصفية
2. **الوصف:** اكتب وصف مناسب للصفحات
3. **الكلمات المفتاحية:** استخدم كلمات مناسبة لمجالك

---

## 🎯 خطة الصيانة

### 📅 صيانة أسبوعية:
- [ ] فحص عمل جميع الروابط
- [ ] تحديث المنتجات الجديدة
- [ ] مراجعة رسائل العملاء

### 📅 صيانة شهرية:
- [ ] عمل نسخة احتياطية كاملة
- [ ] فحص سرعة الموقع
- [ ] تحديث معلومات الاتصال

### 📅 صيانة سنوية:
- [ ] تجديد استضافة الموقع
- [ ] مراجعة التصميم والألوان
- [ ] إضافة ميزات جديدة

---

**💡 نصيحة أخيرة:** احتفظ دائماً بنسخة احتياطية من موقعك قبل إجراء أي تعديلات كبيرة!
