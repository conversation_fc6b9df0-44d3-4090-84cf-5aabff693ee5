// الملف الرئيسي للجافاسكريبت
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التطبيق
    initializeApp();

    // تحميل المنتجات والأعمال
    loadProducts();
    loadWorks();

    // ربط الأحداث
    bindEvents();

    // تطبيق الترجمات
    applyTranslations();

    // تطبيق الثيم المحفوظ
    applyTheme();

    // إضافة تأثيرات التمرير
    addScrollEffects();
});

// تهيئة التطبيق
function initializeApp() {
    // إظهار مؤشر التحميل
    showLoadingIndicator();

    // تحديد اللغة الافتراضية
    const savedLanguage = localStorage.getItem('language') || 'ar';
    setLanguage(savedLanguage);

    // تحديد الثيم الافتراضي
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);

    // إخفاء مؤشر التحميل بعد التحميل
    setTimeout(() => {
        hideLoadingIndicator();
    }, 500);
}

// إظهار مؤشر التحميل
function showLoadingIndicator() {
    const loader = document.createElement('div');
    loader.id = 'page-loader';
    loader.innerHTML = `
        <div class="loader-content">
            <div class="loading-spinner"></div>
            <p data-key="loading">جاري التحميل...</p>
        </div>
    `;
    loader.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--bg-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.3s ease;
    `;
    document.body.appendChild(loader);
}

// إخفاء مؤشر التحميل
function hideLoadingIndicator() {
    const loader = document.getElementById('page-loader');
    if (loader) {
        loader.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(loader);
        }, 300);
    }
}

// إدارة اللغات
function setLanguage(lang) {
    localStorage.setItem('language', lang);

    // تحديث العلم في زر اللغة
    const currentLangElement = document.querySelector('.current-lang');
    const flags = { ar: '🇸🇦', en: '🇺🇸', tr: '🇹🇷' };
    if (currentLangElement) {
        currentLangElement.textContent = flags[lang];
    }

    // تطبيق الترجمات
    applyTranslations(lang);

    // إعادة تحميل المحتوى الديناميكي
    loadProducts();
    loadWorks();

    // تحديث السلة
    if (window.cart) {
        cart.renderCartItems();
    }
}

// إدارة الثيمات
function setTheme(theme) {
    localStorage.setItem('theme', theme);

    // إزالة جميع فئات الثيم
    document.body.classList.remove('light-mode', 'dark-mode');

    // إضافة فئة الثيم الجديد
    document.body.classList.add(theme + '-mode');

    // تحديث أيقونة الثيم
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        const icon = themeToggle.querySelector('i');
        if (icon) {
            icon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // تحديث لون meta theme-color للمتصفحات المحمولة
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
        metaThemeColor = document.createElement('meta');
        metaThemeColor.name = 'theme-color';
        document.head.appendChild(metaThemeColor);
    }
    metaThemeColor.content = theme === 'light' ? '#ffffff' : '#0f172a';
}

function applyTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
}

// تحميل المنتجات
function loadProducts() {
    const productsGrid = document.getElementById('products-grid');
    if (!productsGrid) return;

    const currentLang = getCurrentLanguage();
    const featuredProducts = products.filter(product => product.featured);

    productsGrid.innerHTML = featuredProducts.map(product => `
        <div class="product-card fade-in">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name[currentLang]}" loading="lazy">
                ${product.featured ? `<div class="product-badge">${currentLang === 'ar' ? 'مميز' : currentLang === 'en' ? 'Featured' : 'Öne Çıkan'}</div>` : ''}
            </div>
            <div class="product-info">
                <div class="product-category">${product.category[currentLang]}</div>
                <h3 class="product-title">${product.name[currentLang]}</h3>
                <p class="product-description">${product.description[currentLang]}</p>
                <div class="product-footer">
                    <span class="product-price">$${product.price}</span>
                    <button class="add-to-cart" data-product-id="${product.id}">
                        <i class="fas fa-shopping-cart"></i>
                        ${t('addToCart')}
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// تحميل الأعمال
function loadWorks() {
    const worksGrid = document.getElementById('works-grid');
    if (!worksGrid) return;

    const currentLang = getCurrentLanguage();

    worksGrid.innerHTML = works.slice(0, 6).map(work => `
        <div class="work-card fade-in">
            <div class="work-image">
                <img src="${work.image}" alt="${work.title[currentLang]}" loading="lazy">
                <div class="work-badge">${work.category[currentLang]}</div>
            </div>
            <div class="work-info">
                <div class="work-category">${work.category[currentLang]}</div>
                <h3 class="work-title">${work.title[currentLang]}</h3>
                <p class="work-description">${work.description[currentLang]}</p>
                <div class="work-footer">
                    <div class="work-meta">
                        <span><i class="fas fa-user"></i> ${work.client[currentLang]}</span>
                        <span><i class="fas fa-calendar"></i> ${work.year}</span>
                    </div>
                    <button class="view-work-btn">
                        <i class="fas fa-eye"></i>
                        ${currentLang === 'ar' ? 'عرض المشروع' : currentLang === 'en' ? 'View Project' : 'Projeyi Görüntüle'}
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// ربط الأحداث
function bindEvents() {
    // تبديل القائمة المحمولة
    const mobileToggle = document.getElementById('mobile-toggle');
    const navMenu = document.getElementById('nav-menu');

    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', () => {
            mobileToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // إغلاق القائمة عند النقر على رابط
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (mobileToggle && navMenu) {
                mobileToggle.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    });

    // تبديل قائمة اللغات
    const langBtn = document.getElementById('lang-btn');
    const langMenu = document.getElementById('lang-menu');

    if (langBtn && langMenu) {
        langBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            langMenu.classList.toggle('active');
        });

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', () => {
            langMenu.classList.remove('active');
        });

        langMenu.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    // تغيير اللغة
    const langOptions = document.querySelectorAll('.lang-option');
    langOptions.forEach(option => {
        option.addEventListener('click', () => {
            const lang = option.getAttribute('data-lang');
            setLanguage(lang);
            langMenu.classList.remove('active');
        });
    });

    // تبديل الثيم
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', () => {
            const currentTheme = localStorage.getItem('theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            setTheme(newTheme);
        });
    }

    // التمرير السلس للروابط
    const smoothScrollLinks = document.querySelectorAll('a[href^="#"]');
    smoothScrollLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // إرسال نموذج الاتصال
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }

    // تحديث الرابط النشط في التنقل
    updateActiveNavLink();
    window.addEventListener('scroll', updateActiveNavLink);
}

// معالجة نموذج الاتصال
function handleContactForm(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {
        name: formData.get('name'),
        email: formData.get('email'),
        subject: formData.get('subject'),
        message: formData.get('message')
    };

    // محاكاة إرسال النموذج
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${t('loading')}`;
    submitBtn.disabled = true;

    setTimeout(() => {
        // إظهار رسالة نجاح
        showNotification(
            getCurrentLanguage() === 'ar'
                ? 'تم إرسال رسالتك بنجاح! سنقوم بالرد عليك قريباً.'
                : getCurrentLanguage() === 'en'
                ? 'Your message has been sent successfully! We will reply to you soon.'
                : 'Mesajınız başarıyla gönderildi! Size yakında cevap vereceğiz.',
            'success'
        );

        // إعادة تعيين النموذج
        e.target.reset();

        // إعادة تعيين الزر
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

// تحديث الرابط النشط في التنقل
function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');

    let currentSection = '';
    const scrollPosition = window.scrollY + 100;

    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;

        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

// إضافة تأثيرات التمرير
function addScrollEffects() {
    // تأثير الهيدر عند التمرير
    const header = document.querySelector('.header');

    window.addEventListener('scroll', () => {
        const scrolled = window.scrollY > 100;
        const currentTheme = localStorage.getItem('theme') || 'light';

        if (scrolled) {
            if (currentTheme === 'dark') {
                header.style.background = 'rgba(15, 23, 42, 0.95)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
            }
            header.style.backdropFilter = 'blur(10px)';
            header.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
        } else {
            header.style.background = '';
            header.style.backdropFilter = '';
            header.style.boxShadow = '';
        }
    });

    // تأثير الظهور عند التمرير المحسن
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                // إضافة تأخير تدريجي للعناصر
                setTimeout(() => {
                    entry.target.classList.add('scroll-reveal', 'revealed');
                }, index * 100);
            }
        });
    }, observerOptions);

    // مراقبة العناصر مع إضافة فئة scroll-reveal
    const elementsToAnimate = document.querySelectorAll('.service-card, .product-card, .work-card, .feature, .contact-item');
    elementsToAnimate.forEach((el, index) => {
        el.classList.add('scroll-reveal');
        observer.observe(el);
    });

    // تأثير parallax للهيرو
    const hero = document.querySelector('.hero');
    if (hero) {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        });
    }
}

// دالة عرض الإشعارات
function showNotification(message, type = 'info') {
    if (window.cart) {
        cart.showNotification(message, type);
    }
}

// دالة مساعدة للحصول على معاملات URL
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    return Object.fromEntries(params.entries());
}

// دالة لتحديث URL بدون إعادة تحميل الصفحة
function updateUrl(params) {
    const url = new URL(window.location);
    Object.keys(params).forEach(key => {
        if (params[key]) {
            url.searchParams.set(key, params[key]);
        } else {
            url.searchParams.delete(key);
        }
    });
    window.history.replaceState({}, '', url);
}

// دالة لتنسيق الأرقام
function formatNumber(num) {
    return new Intl.NumberFormat(getCurrentLanguage() === 'ar' ? 'ar-SA' : getCurrentLanguage()).format(num);
}

// دالة لتنسيق التاريخ
function formatDate(date) {
    const lang = getCurrentLanguage();
    const locale = lang === 'ar' ? 'ar-SA' : lang === 'tr' ? 'tr-TR' : 'en-US';
    return new Intl.DateTimeFormat(locale).format(new Date(date));
}

// دالة للتحقق من دعم الميزات
function checkFeatureSupport() {
    return {
        webp: (() => {
            const canvas = document.createElement('canvas');
            return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
        })(),
        intersectionObserver: 'IntersectionObserver' in window,
        localStorage: (() => {
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                return true;
            } catch (e) {
                return false;
            }
        })()
    };
}

// تهيئة ميزات إضافية
const features = checkFeatureSupport();

// إضافة دعم للصور WebP إذا كان متاحاً
if (features.webp) {
    document.documentElement.classList.add('webp-support');
}

// إضافة معالج للأخطاء
window.addEventListener('error', (e) => {
    console.error('خطأ في التطبيق:', e.error);
});

// إضافة معالج لأخطاء الوعود
window.addEventListener('unhandledrejection', (e) => {
    console.error('وعد مرفوض:', e.reason);
});

// تصدير الدوال للاستخدام العام
window.appFunctions = {
    setLanguage,
    setTheme,
    showNotification,
    formatNumber,
    formatDate
};
