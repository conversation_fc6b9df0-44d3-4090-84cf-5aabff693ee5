<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - Tab3h</title>
    <meta name="description" content="لوحة التحكم الرئيسية">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/enhancements.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/professional-enhancements.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="css/dashboard.css">
</head>
<body class="light-mode dashboard-page">
    <!-- Dashboard Header -->
    <header class="dashboard-header" id="dashboard-header">
        <div class="dashboard-header-content">
            <!-- Logo -->
            <div class="dashboard-logo">
                <a href="index.html" class="logo-link">
                    <div class="logo-icon">T</div>
                    <span class="logo-text">Tab3h</span>
                </a>
            </div>

            <!-- Header Title -->
            <div class="header-title">
                <h1 data-key="dashboardTitle">لوحة التحكم</h1>
                <span class="breadcrumb" data-key="dashboardBreadcrumb">الرئيسية / لوحة التحكم</span>
            </div>

            <!-- Header Controls -->
            <div class="header-controls">
                <!-- Notifications -->
                <div class="notification-dropdown">
                    <button type="button" class="notification-btn" id="notification-btn" title="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="notification-menu" id="notification-menu">
                        <div class="notification-header">
                            <h3 data-key="notifications">الإشعارات</h3>
                            <span class="notification-count">3 جديدة</span>
                        </div>
                        <div class="notification-list">
                            <div class="notification-item">
                                <div class="notification-icon success">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="notification-content">
                                    <h4>طلب جديد</h4>
                                    <p>تم استلام طلب شراء جديد</p>
                                    <span class="notification-time">منذ 5 دقائق</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon info">
                                    <i class="fas fa-info"></i>
                                </div>
                                <div class="notification-content">
                                    <h4>تحديث النظام</h4>
                                    <p>تم تحديث النظام بنجاح</p>
                                    <span class="notification-time">منذ ساعة</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon warning">
                                    <i class="fas fa-exclamation"></i>
                                </div>
                                <div class="notification-content">
                                    <h4>تنبيه المخزون</h4>
                                    <p>بعض المنتجات قاربت على النفاد</p>
                                    <span class="notification-time">منذ 3 ساعات</span>
                                </div>
                            </div>
                        </div>
                        <div class="notification-footer">
                            <a href="#" data-key="viewAllNotifications">عرض جميع الإشعارات</a>
                        </div>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="user-dropdown">
                    <button type="button" class="user-btn" id="user-btn" title="قائمة المستخدم">
                        <img src="" alt="صورة المستخدم" class="user-avatar" id="user-avatar">
                        <span class="user-name" id="user-name">مستخدم</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-menu" id="user-menu">
                        <div class="user-info">
                            <img src="" alt="صورة المستخدم" class="user-avatar-large" id="user-avatar-large">
                            <div class="user-details">
                                <h4 id="user-name-full">اسم المستخدم</h4>
                                <span id="user-email"><EMAIL></span>
                            </div>
                        </div>
                        <div class="user-menu-items">
                            <a href="#" class="user-menu-item">
                                <i class="fas fa-user"></i>
                                <span data-key="profile">الملف الشخصي</span>
                            </a>
                            <a href="#" class="user-menu-item">
                                <i class="fas fa-cog"></i>
                                <span data-key="settings">الإعدادات</span>
                            </a>
                            <div class="user-menu-divider"></div>
                            <button type="button" class="user-menu-item logout-btn" id="logout-btn">
                                <i class="fas fa-sign-out-alt"></i>
                                <span data-key="logout">تسجيل الخروج</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Theme Toggle -->
                <button type="button" class="theme-toggle" id="theme-toggle" title="تبديل الوضع الليلي" aria-label="تبديل الوضع الليلي">
                    <i class="fas fa-moon"></i>
                </button>

                <!-- Language Selector -->
                <div class="language-selector">
                    <button type="button" class="lang-btn" id="lang-btn" title="تغيير اللغة" aria-label="تغيير اللغة">
                        <i class="fas fa-globe"></i>
                        <span class="current-lang">🇸🇦</span>
                    </button>
                    <div class="lang-menu" id="lang-menu">
                        <button type="button" class="lang-option" data-lang="ar">
                            <span class="flag">🇸🇦</span>
                            <span>العربية</span>
                        </button>
                        <button type="button" class="lang-option" data-lang="en">
                            <span class="flag">🇺🇸</span>
                            <span>English</span>
                        </button>
                        <button type="button" class="lang-option" data-lang="tr">
                            <span class="flag">🇹🇷</span>
                            <span>Türkçe</span>
                        </button>
                    </div>
                </div>

                <!-- Mobile Menu Toggle -->
                <button type="button" class="mobile-menu-toggle" id="mobile-menu-toggle" title="قائمة التنقل">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Dashboard Layout -->
    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar" id="dashboard-sidebar">
            <nav class="sidebar-nav">
                <ul class="sidebar-menu">
                    <li class="sidebar-item active">
                        <a href="#products" class="sidebar-link" data-section="products">
                            <i class="fas fa-box"></i>
                            <span data-key="products">إدارة المنتجات</span>
                        </a>
                    </li>
                </ul>

                <div class="sidebar-footer">
                    <a href="index.html" class="sidebar-link">
                        <i class="fas fa-home"></i>
                        <span data-key="backToSite">العودة للموقع</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Products Section -->
            <section class="dashboard-section active" id="section-products">
                <div class="section-header">
                    <h2 data-key="productsManagement">إدارة المنتجات</h2>
                    <button type="button" class="btn btn-gradient" id="add-product-btn">
                        <i class="fas fa-plus"></i>
                        <span data-key="addProduct">إضافة منتج جديد</span>
                    </button>
                </div>

                <!-- Products Stats -->
                <div class="products-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="total-products">12</h3>
                            <p class="stat-label" data-key="totalProducts">إجمالي المنتجات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="published-products">10</h3>
                            <p class="stat-label" data-key="publishedProducts">منتجات منشورة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-eye-slash"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="draft-products">2</h3>
                            <p class="stat-label" data-key="draftProducts">مسودات</p>
                        </div>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="products-table-container">
                    <div class="table-header">
                        <div class="table-search">
                            <input type="text" id="search-products" placeholder="البحث في المنتجات..." class="search-input">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="table-filters">
                            <select id="filter-category" class="filter-select" title="تصفية حسب الفئة" aria-label="تصفية حسب الفئة">
                                <option value="">جميع الفئات</option>
                            </select>
                            <select id="filter-status" class="filter-select" title="تصفية حسب الحالة" aria-label="تصفية حسب الحالة">
                                <option value="">جميع الحالات</option>
                                <option value="published">منشور</option>
                                <option value="draft">مسودة</option>
                            </select>
                        </div>
                    </div>

                    <table class="data-table" id="products-table">
                        <thead>
                            <tr>
                                <th data-key="productImage">الصورة</th>
                                <th data-key="productName">اسم المنتج</th>
                                <th data-key="category">الفئة</th>
                                <th data-key="price">السعر</th>
                                <th data-key="status">الحالة</th>
                                <th data-key="actions">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="products-table-body">
                            <!-- سيتم تحميل المنتجات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

    <!-- Product Modal -->
    <div class="modal" id="product-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title" data-key="addProduct">إضافة منتج جديد</h3>
                <button type="button" class="modal-close" id="modal-close" title="إغلاق" aria-label="إغلاق النافذة">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="product-form" class="product-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-name" class="form-label" data-key="productName">اسم المنتج</label>
                            <input type="text" id="product-name" name="name" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="product-category" class="form-label" data-key="category">الفئة</label>
                            <select id="product-category" name="category" class="form-input" required>
                                <option value="">اختر الفئة</option>
                                <option value="طباعة">طباعة</option>
                                <option value="تصميم">تصميم</option>
                                <option value="تطوير">تطوير</option>
                                <option value="تسويق">تسويق</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-price" class="form-label" data-key="price">السعر</label>
                            <input type="number" id="product-price" name="price" class="form-input" min="0" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="product-status" class="form-label" data-key="status">الحالة</label>
                            <select id="product-status" name="status" class="form-input" required>
                                <option value="published">منشور</option>
                                <option value="draft">مسودة</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="product-description" class="form-label" data-key="description">الوصف</label>
                        <textarea id="product-description" name="description" class="form-input" rows="4" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="product-image" class="form-label" data-key="productImage">رابط الصورة</label>
                        <input type="url" id="product-image" name="image" class="form-input" placeholder="https://example.com/image.jpg">
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" id="cancel-btn" data-key="cancel">إلغاء</button>
                        <button type="submit" class="btn btn-gradient" id="save-btn" data-key="save">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" id="delete-modal">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3 data-key="confirmDelete">تأكيد الحذف</h3>
                <button type="button" class="modal-close" id="delete-modal-close" title="إغلاق" aria-label="إغلاق النافذة">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="delete-confirmation">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p data-key="deleteMessage">هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-outline" id="cancel-delete-btn" data-key="cancel">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn" data-key="delete">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/translations.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
