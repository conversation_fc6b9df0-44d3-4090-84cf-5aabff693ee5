<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - Tab3h</title>
    <meta name="description" content="لوحة التحكم الرئيسية">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/enhancements.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/professional-enhancements.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="css/dashboard.css">
</head>
<body class="light-mode dashboard-page">
    <!-- Dashboard Header -->
    <header class="dashboard-header" id="dashboard-header">
        <div class="dashboard-header-content">
            <!-- Logo -->
            <div class="dashboard-logo">
                <a href="index.html" class="logo-link">
                    <div class="logo-icon">T</div>
                    <span class="logo-text">Tab3h</span>
                </a>
            </div>

            <!-- Header Title -->
            <div class="header-title">
                <h1 data-key="dashboardTitle">لوحة التحكم</h1>
                <span class="breadcrumb" data-key="dashboardBreadcrumb">الرئيسية / لوحة التحكم</span>
            </div>

            <!-- Header Controls -->
            <div class="header-controls">
                <!-- Notifications -->
                <div class="notification-dropdown">
                    <button type="button" class="notification-btn" id="notification-btn" title="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="notification-menu" id="notification-menu">
                        <div class="notification-header">
                            <h3 data-key="notifications">الإشعارات</h3>
                            <span class="notification-count">3 جديدة</span>
                        </div>
                        <div class="notification-list">
                            <div class="notification-item">
                                <div class="notification-icon success">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="notification-content">
                                    <h4>طلب جديد</h4>
                                    <p>تم استلام طلب شراء جديد</p>
                                    <span class="notification-time">منذ 5 دقائق</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon info">
                                    <i class="fas fa-info"></i>
                                </div>
                                <div class="notification-content">
                                    <h4>تحديث النظام</h4>
                                    <p>تم تحديث النظام بنجاح</p>
                                    <span class="notification-time">منذ ساعة</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon warning">
                                    <i class="fas fa-exclamation"></i>
                                </div>
                                <div class="notification-content">
                                    <h4>تنبيه المخزون</h4>
                                    <p>بعض المنتجات قاربت على النفاد</p>
                                    <span class="notification-time">منذ 3 ساعات</span>
                                </div>
                            </div>
                        </div>
                        <div class="notification-footer">
                            <a href="#" data-key="viewAllNotifications">عرض جميع الإشعارات</a>
                        </div>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="user-dropdown">
                    <button type="button" class="user-btn" id="user-btn" title="قائمة المستخدم">
                        <img src="" alt="صورة المستخدم" class="user-avatar" id="user-avatar">
                        <span class="user-name" id="user-name">مستخدم</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-menu" id="user-menu">
                        <div class="user-info">
                            <img src="" alt="صورة المستخدم" class="user-avatar-large" id="user-avatar-large">
                            <div class="user-details">
                                <h4 id="user-name-full">اسم المستخدم</h4>
                                <span id="user-email"><EMAIL></span>
                            </div>
                        </div>
                        <div class="user-menu-items">
                            <a href="#" class="user-menu-item">
                                <i class="fas fa-user"></i>
                                <span data-key="profile">الملف الشخصي</span>
                            </a>
                            <a href="#" class="user-menu-item">
                                <i class="fas fa-cog"></i>
                                <span data-key="settings">الإعدادات</span>
                            </a>
                            <div class="user-menu-divider"></div>
                            <button type="button" class="user-menu-item logout-btn" id="logout-btn">
                                <i class="fas fa-sign-out-alt"></i>
                                <span data-key="logout">تسجيل الخروج</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Theme Toggle -->
                <button type="button" class="theme-toggle" id="theme-toggle" title="تبديل الوضع الليلي" aria-label="تبديل الوضع الليلي">
                    <i class="fas fa-moon"></i>
                </button>

                <!-- Language Selector -->
                <div class="language-selector">
                    <button type="button" class="lang-btn" id="lang-btn" title="تغيير اللغة" aria-label="تغيير اللغة">
                        <i class="fas fa-globe"></i>
                        <span class="current-lang">🇸🇦</span>
                    </button>
                    <div class="lang-menu" id="lang-menu">
                        <button type="button" class="lang-option" data-lang="ar">
                            <span class="flag">🇸🇦</span>
                            <span>العربية</span>
                        </button>
                        <button type="button" class="lang-option" data-lang="en">
                            <span class="flag">🇺🇸</span>
                            <span>English</span>
                        </button>
                        <button type="button" class="lang-option" data-lang="tr">
                            <span class="flag">🇹🇷</span>
                            <span>Türkçe</span>
                        </button>
                    </div>
                </div>

                <!-- Mobile Menu Toggle -->
                <button type="button" class="mobile-menu-toggle" id="mobile-menu-toggle" title="قائمة التنقل">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Dashboard Layout -->
    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar" id="dashboard-sidebar">
            <nav class="sidebar-nav">
                <ul class="sidebar-menu">
                    <li class="sidebar-item active">
                        <a href="#dashboard" class="sidebar-link" data-section="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span data-key="dashboard">لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#products" class="sidebar-link" data-section="products">
                            <i class="fas fa-box"></i>
                            <span data-key="products">المنتجات</span>
                        </a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#orders" class="sidebar-link" data-section="orders">
                            <i class="fas fa-shopping-cart"></i>
                            <span data-key="orders">الطلبات</span>
                        </a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#customers" class="sidebar-link" data-section="customers">
                            <i class="fas fa-users"></i>
                            <span data-key="customers">العملاء</span>
                        </a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#analytics" class="sidebar-link" data-section="analytics">
                            <i class="fas fa-chart-bar"></i>
                            <span data-key="analytics">التحليلات</span>
                        </a>
                    </li>
                    <li class="sidebar-item">
                        <a href="#settings" class="sidebar-link" data-section="settings">
                            <i class="fas fa-cog"></i>
                            <span data-key="settings">الإعدادات</span>
                        </a>
                    </li>
                </ul>
                
                <div class="sidebar-footer">
                    <a href="index.html" class="sidebar-link">
                        <i class="fas fa-home"></i>
                        <span data-key="backToSite">العودة للموقع</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Dashboard Overview -->
            <section class="dashboard-section active" id="section-dashboard">
                <div class="section-header">
                    <h2 data-key="overview">نظرة عامة</h2>
                    <p data-key="overviewDesc">ملخص سريع لأداء موقعك</p>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">156</h3>
                            <p class="stat-label" data-key="totalOrders">إجمالي الطلبات</p>
                            <span class="stat-change positive">+12%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">$12,450</h3>
                            <p class="stat-label" data-key="totalRevenue">إجمالي الإيرادات</p>
                            <span class="stat-change positive">+8%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">89</h3>
                            <p class="stat-label" data-key="totalCustomers">إجمالي العملاء</p>
                            <span class="stat-change positive">+15%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">12</h3>
                            <p class="stat-label" data-key="totalProducts">إجمالي المنتجات</p>
                            <span class="stat-change neutral">0%</span>
                        </div>
                    </div>
                </div>

                <!-- Charts and Recent Activity -->
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 data-key="salesChart">مخطط المبيعات</h3>
                            <div class="card-actions">
                                <button type="button" class="btn-icon" title="تحديث">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-line"></i>
                                <p data-key="chartPlaceholder">سيتم عرض المخطط هنا</p>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 data-key="recentOrders">الطلبات الأخيرة</h3>
                            <a href="#orders" class="view-all-link" data-key="viewAll">عرض الكل</a>
                        </div>
                        <div class="card-content">
                            <div class="recent-orders" id="recent-orders">
                                <!-- سيتم تحميل الطلبات ديناميكياً -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Products Section -->
            <section class="dashboard-section" id="section-products">
                <div class="section-header">
                    <h2 data-key="productsManagement">إدارة المنتجات</h2>
                    <button type="button" class="btn btn-gradient" id="add-product-btn">
                        <i class="fas fa-plus"></i>
                        <span data-key="addProduct">إضافة منتج</span>
                    </button>
                </div>
                <div class="products-table-container">
                    <table class="data-table" id="products-table">
                        <thead>
                            <tr>
                                <th data-key="productName">اسم المنتج</th>
                                <th data-key="category">الفئة</th>
                                <th data-key="price">السعر</th>
                                <th data-key="status">الحالة</th>
                                <th data-key="actions">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="products-table-body">
                            <!-- سيتم تحميل المنتجات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Other sections will be added here -->
            <section class="dashboard-section" id="section-orders">
                <div class="section-header">
                    <h2 data-key="ordersManagement">إدارة الطلبات</h2>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-tools"></i>
                    <h3 data-key="comingSoon">قريباً</h3>
                    <p data-key="comingSoonDesc">هذا القسم قيد التطوير</p>
                </div>
            </section>

            <section class="dashboard-section" id="section-customers">
                <div class="section-header">
                    <h2 data-key="customersManagement">إدارة العملاء</h2>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-tools"></i>
                    <h3 data-key="comingSoon">قريباً</h3>
                    <p data-key="comingSoonDesc">هذا القسم قيد التطوير</p>
                </div>
            </section>

            <section class="dashboard-section" id="section-analytics">
                <div class="section-header">
                    <h2 data-key="analyticsReports">التقارير والتحليلات</h2>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-tools"></i>
                    <h3 data-key="comingSoon">قريباً</h3>
                    <p data-key="comingSoonDesc">هذا القسم قيد التطوير</p>
                </div>
            </section>

            <section class="dashboard-section" id="section-settings">
                <div class="section-header">
                    <h2 data-key="systemSettings">إعدادات النظام</h2>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-tools"></i>
                    <h3 data-key="comingSoon">قريباً</h3>
                    <p data-key="comingSoonDesc">هذا القسم قيد التطوير</p>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/translations.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
